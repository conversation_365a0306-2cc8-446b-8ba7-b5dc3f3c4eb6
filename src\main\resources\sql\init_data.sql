-- 万行公益基金会数据库初始化脚本

-- 1. 优化活动表结构（如果需要添加字段）
-- ALTER TABLE activities ADD COLUMN category VARCHAR(50) DEFAULT 'training' COMMENT '活动类型：training-培训, donation-捐赠, volunteer-志愿服务';

-- 2. 插入万行基金会风格的测试活动数据
INSERT INTO activities (title, content, image, start_time, end_time, location, participant_limit, current_participants, status, organizer_id) VALUES 
('AED急救培训走进深圳湾创业广场', 
'应深圳湾创业广场党委邀请，万行公益基金会举办急救知识培训活动，重点培训自动体外除颤器（AED）的使用能力。本次培训将邀请专业的急救医师和培训师，通过理论讲解和实操演练相结合的方式，让参与者掌握基本的心肺复苏技能和AED设备的正确使用方法。培训结束后，参与者将获得急救技能认证证书。', 
'https://picsum.photos/600/400?random=1', 
'2025-06-05 14:00:00', 
'2025-06-05 17:00:00', 
'深圳湾创业广场', 
50, 25, 0, 5),

('万众益心，救在身边 - 社区AED设备捐赠', 
'为社区配置AED设备并开展急救知识培训，让更多人懂急救、会急救，提升社区应急救护能力。本次活动将为万科城小区捐赠2台AED设备，并为小区居民提供免费的急救知识培训。活动包括设备安装、使用培训、日常维护指导等环节。', 
'https://picsum.photos/600/400?random=2', 
'2025-05-30 09:00:00', 
'2025-05-30 12:00:00', 
'万科城小区', 
30, 18, 1, 5),

('应急医疗救援培训导师师资班', 
'培养专业的急救培训导师，提升志愿者的急救教学能力，为社区急救培训提供师资保障。本次师资班将邀请国内知名急救专家授课，内容包括急救理论、教学方法、实操技能、心理疏导等多个方面。完成培训的学员将获得急救培训导师资格认证。', 
'https://picsum.photos/600/400?random=3', 
'2025-05-28 09:00:00', 
'2025-05-30 17:00:00', 
'宝安区应急医疗救援培训中心', 
20, 20, 2, 5),

('社区急救知识普及讲座', 
'面向社区居民开展急救知识普及讲座，提高公众对急救重要性的认识，推广急救技能。讲座将通过生动的案例分析、互动问答、现场演示等方式，让居民了解常见急救场景的处理方法，掌握基本的急救技能。', 
'https://picsum.photos/600/400?random=4', 
'2025-06-15 19:00:00', 
'2025-06-15 21:00:00', 
'南山区文化中心', 
100, 8, 0, 5),

('校园AED设备维护志愿服务', 
'组织志愿者对校园内的AED设备进行定期检查和维护，确保设备正常运行，保障校园安全。志愿者将接受专业的设备维护培训，学习AED设备的日常检查、清洁保养、故障排除等技能。', 
'https://picsum.photos/600/400?random=5', 
'2025-05-20 14:00:00', 
'2025-05-20 16:00:00', 
'深圳大学', 
15, 15, 2, 5),

('企业员工急救技能培训', 
'为企业员工提供专业的急救技能培训，包括心肺复苏、AED使用等，提升企业应急救护水平。培训将结合企业实际工作环境，设计针对性的急救场景演练，帮助员工掌握实用的急救技能。', 
'https://picsum.photos/600/400?random=6', 
'2025-06-20 14:00:00', 
'2025-06-20 17:00:00', 
'腾讯滨海大厦', 
40, 12, 0, 5);

-- 3. 插入组织数据
INSERT INTO organizations (name, description, contact_person, contact_phone, logo, user_id) VALUES 
('万行公益基金会', 
'万行公益基金会致力于社区公益服务，专注于急救培训和AED设备捐赠，提升社区应急救护能力。我们的使命是让更多人掌握急救技能，在关键时刻能够挽救生命。', 
'张主任', 
'0755-12345678', 
'default_logo.png', 
5);

-- 4. 插入志愿服务数据
INSERT INTO volunteer_services (title, description, service_date, location, volunteer_count, organizer_id) VALUES 
('社区急救设备巡检服务', 
'定期检查社区内AED设备的运行状态，确保设备正常可用', 
'2025-06-01', 
'各社区AED设备点', 
5, 5),

('急救知识宣传服务', 
'在社区、学校、企业开展急救知识宣传活动', 
'2025-06-10', 
'南山区各社区', 
10, 5);

-- 5. 更新用户角色（将用户5设为组织管理员）
UPDATE users SET role_id = 3 WHERE id = 5;
