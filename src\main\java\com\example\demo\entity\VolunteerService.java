package com.example.demo.entity;

import java.math.BigDecimal;
import java.util.Date;

public class VolunteerService {
    private Long id;
    private Long userId;
    private Long activityId;
    private BigDecimal serviceHours;
    private Integer evaluation;
    private Integer isCompleted;
    private Date createTime;

    // getter和setter
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }
    
    public Long getUserId() { return userId; }
    public void setUserId(Long userId) { this.userId = userId; }
    
    public Long getActivityId() { return activityId; }
    public void setActivityId(Long activityId) { this.activityId = activityId; }
    
    public BigDecimal getServiceHours() { return serviceHours; }
    public void setServiceHours(BigDecimal serviceHours) { this.serviceHours = serviceHours; }
    
    public Integer getEvaluation() { return evaluation; }
    public void setEvaluation(Integer evaluation) { this.evaluation = evaluation; }
    
    public Integer getIsCompleted() { return isCompleted; }
    public void setIsCompleted(Integer isCompleted) { this.isCompleted = isCompleted; }
    
    public Date getCreateTime() { return createTime; }
    public void setCreateTime(Date createTime) { this.createTime = createTime; }
} 