package com.example.demo.controller;

import com.example.demo.entity.User;
import com.example.demo.service.UserService;
import com.example.demo.service.ActivityService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.util.*;

@CrossOrigin(origins = "*")

@RestController
@RequestMapping("/api/user")
public class UserController {
    private static final Logger logger = LoggerFactory.getLogger(UserController.class);

    @Autowired
    private UserService userService;

    @Autowired
    private ActivityService activityService;

    @PostMapping("/register")
    public ResponseEntity<String> register(@RequestBody User user) {
        try {
            logger.info("接收到注册请求: {}", user.getUsername());
            boolean success = userService.register(user);
            if (success) {
                logger.info("用户注册成功: {}", user.getUsername());
                return ResponseEntity.ok("注册成功");
            } else {
                logger.warn("用户名已存在: {}", user.getUsername());
                return ResponseEntity.badRequest().body("用户名已存在");
            }
        } catch (Exception e) {
            logger.error("注册过程中发生错误: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("注册失败: " + e.getMessage());
        }
    }

    @PostMapping("/login")
    public ResponseEntity<?> login(@RequestBody Map<String, String> request) {
        try {
            String username = request.get("username");
            String password = request.get("password");

            logger.info("接收到登录请求: {}", username);
            User loggedInUser = userService.login(username, password);
            if (loggedInUser != null) {
                Map<String, Object> result = new HashMap<>();
                result.put("id", loggedInUser.getId());
                result.put("username", loggedInUser.getUsername());
                result.put("email", loggedInUser.getEmail());
                result.put("phone", loggedInUser.getPhone());
                result.put("avatar", loggedInUser.getAvatar());
                result.put("points", loggedInUser.getPoints());
                result.put("roleId", loggedInUser.getRoleId());

                // 获取用户参与的活动数量
                try {
                    int participationCount = userService.getUserParticipationCount(loggedInUser.getId());
                    result.put("participatedActivities", participationCount);
                } catch (Exception e) {
                    logger.warn("获取用户参与活动数量失败: {}", e.getMessage());
                    result.put("participatedActivities", 0);
                }

                logger.info("用户登录成功: {}", username);
                return ResponseEntity.ok(result);
            } else {
                logger.warn("登录失败，用户名或密码错误: {}", username);
                return ResponseEntity.badRequest().body(Map.of("error", "用户名或密码错误"));
            }
        } catch (Exception e) {
            logger.error("登录过程中发生错误: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Map.of("error", "登录失败: " + e.getMessage()));
        }
    }

    @GetMapping("/{id}")
    public ResponseEntity<?> getById(@PathVariable Integer id) {
        try {
            logger.info("查询用户ID: {}", id);
            User user = userService.getById(id);
            if (user != null) {
                return ResponseEntity.ok(user);
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            logger.error("查询用户时发生错误: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("查询失败: " + e.getMessage());
        }
    }

    @GetMapping("/all")
    public ResponseEntity<?> getAll() {
        try {
            logger.info("查询所有用户");
            List<User> users = userService.getAll();
            return ResponseEntity.ok(users);
        } catch (Exception e) {
            logger.error("查询所有用户时发生错误: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("查询失败: " + e.getMessage());
        }
    }

    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public String handleException(Exception e) {
        logger.error("处理请求时发生未捕获的异常: {}", e.getMessage(), e);
        return "操作失败: " + e.getMessage();
    }
}