package com.example.demo.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.io.ClassPathResource;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;

@Component
public class DataInitializer implements CommandLineRunner {

    private static final Logger logger = LoggerFactory.getLogger(DataInitializer.class);

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Override
    public void run(String... args) throws Exception {
        try {
            // 检查数据库连接和基础数据
            Integer activityCount = jdbcTemplate.queryForObject("SELECT COUNT(*) FROM activities", Integer.class);
            Integer userCount = jdbcTemplate.queryForObject("SELECT COUNT(*) FROM users", Integer.class);
            Integer orgCount = jdbcTemplate.queryForObject("SELECT COUNT(*) FROM organizations", Integer.class);

            logger.info("数据库连接成功！");
            logger.info("当前数据统计: 活动 {} 个, 用户 {} 个, 组织 {} 个", activityCount, userCount, orgCount);

            if (activityCount == 0) {
                logger.warn("数据库中没有活动数据，请执行 database_complete_init.sql 脚本进行初始化");
            } else {
                logger.info("数据库初始化完成，系统准备就绪");
            }

        } catch (Exception e) {
            logger.error("数据库连接检查失败: {}", e.getMessage(), e);
            logger.warn("请确保已执行 database_complete_init.sql 脚本创建数据库和表结构");
        }
    }


}
