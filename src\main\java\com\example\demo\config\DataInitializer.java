package com.example.demo.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.io.ClassPathResource;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;

@Component
public class DataInitializer implements CommandLineRunner {
    
    private static final Logger logger = LoggerFactory.getLogger(DataInitializer.class);
    
    @Autowired
    private JdbcTemplate jdbcTemplate;
    
    @Override
    public void run(String... args) throws Exception {
        try {
            // 检查是否已有活动数据
            Integer count = jdbcTemplate.queryForObject("SELECT COUNT(*) FROM activities", Integer.class);
            
            if (count == null || count == 0) {
                logger.info("数据库中没有活动数据，开始初始化...");
                initializeData();
            } else {
                logger.info("数据库中已有 {} 个活动，跳过初始化", count);
            }
            
        } catch (Exception e) {
            logger.error("数据初始化失败: {}", e.getMessage(), e);
        }
    }
    
    private void initializeData() {
        try {
            // 插入万行基金会风格的测试活动数据
            String[] activities = {
                "INSERT INTO activities (title, content, image, start_time, end_time, location, participant_limit, current_participants, status, organizer_id) VALUES " +
                "('AED急救培训走进深圳湾创业广场', " +
                "'应深圳湾创业广场党委邀请，万行公益基金会举办急救知识培训活动，重点培训自动体外除颤器（AED）的使用能力。本次培训将邀请专业的急救医师和培训师，通过理论讲解和实操演练相结合的方式，让参与者掌握基本的心肺复苏技能和AED设备的正确使用方法。培训结束后，参与者将获得急救技能认证证书。', " +
                "'https://picsum.photos/600/400?random=1', " +
                "'2025-06-05 14:00:00', '2025-06-05 17:00:00', '深圳湾创业广场', 50, 25, 0, 1)",
                
                "INSERT INTO activities (title, content, image, start_time, end_time, location, participant_limit, current_participants, status, organizer_id) VALUES " +
                "('万众益心，救在身边 - 社区AED设备捐赠', " +
                "'为社区配置AED设备并开展急救知识培训，让更多人懂急救、会急救，提升社区应急救护能力。本次活动将为万科城小区捐赠2台AED设备，并为小区居民提供免费的急救知识培训。活动包括设备安装、使用培训、日常维护指导等环节。', " +
                "'https://picsum.photos/600/400?random=2', " +
                "'2025-05-30 09:00:00', '2025-05-30 12:00:00', '万科城小区', 30, 18, 1, 1)",
                
                "INSERT INTO activities (title, content, image, start_time, end_time, location, participant_limit, current_participants, status, organizer_id) VALUES " +
                "('应急医疗救援培训导师师资班', " +
                "'培养专业的急救培训导师，提升志愿者的急救教学能力，为社区急救培训提供师资保障。本次师资班将邀请国内知名急救专家授课，内容包括急救理论、教学方法、实操技能、心理疏导等多个方面。完成培训的学员将获得急救培训导师资格认证。', " +
                "'https://picsum.photos/600/400?random=3', " +
                "'2025-05-28 09:00:00', '2025-05-30 17:00:00', '宝安区应急医疗救援培训中心', 20, 20, 2, 1)",
                
                "INSERT INTO activities (title, content, image, start_time, end_time, location, participant_limit, current_participants, status, organizer_id) VALUES " +
                "('社区急救知识普及讲座', " +
                "'面向社区居民开展急救知识普及讲座，提高公众对急救重要性的认识，推广急救技能。讲座将通过生动的案例分析、互动问答、现场演示等方式，让居民了解常见急救场景的处理方法，掌握基本的急救技能。', " +
                "'https://picsum.photos/600/400?random=4', " +
                "'2025-06-15 19:00:00', '2025-06-15 21:00:00', '南山区文化中心', 100, 8, 0, 1)",
                
                "INSERT INTO activities (title, content, image, start_time, end_time, location, participant_limit, current_participants, status, organizer_id) VALUES " +
                "('校园AED设备维护志愿服务', " +
                "'组织志愿者对校园内的AED设备进行定期检查和维护，确保设备正常运行，保障校园安全。志愿者将接受专业的设备维护培训，学习AED设备的日常检查、清洁保养、故障排除等技能。', " +
                "'https://picsum.photos/600/400?random=5', " +
                "'2025-05-20 14:00:00', '2025-05-20 16:00:00', '深圳大学', 15, 15, 2, 1)",
                
                "INSERT INTO activities (title, content, image, start_time, end_time, location, participant_limit, current_participants, status, organizer_id) VALUES " +
                "('企业员工急救技能培训', " +
                "'为企业员工提供专业的急救技能培训，包括心肺复苏、AED使用等，提升企业应急救护水平。培训将结合企业实际工作环境，设计针对性的急救场景演练，帮助员工掌握实用的急救技能。', " +
                "'https://picsum.photos/600/400?random=6', " +
                "'2025-06-20 14:00:00', '2025-06-20 17:00:00', '腾讯滨海大厦', 40, 12, 0, 1)"
            };
            
            for (String sql : activities) {
                try {
                    jdbcTemplate.update(sql);
                    logger.info("成功插入活动数据");
                } catch (Exception e) {
                    logger.warn("插入活动数据失败: {}", e.getMessage());
                }
            }
            
            // 插入组织数据
            try {
                String orgSql = "INSERT INTO organizations (name, description, contact_person, contact_phone, logo, user_id) VALUES " +
                    "('万行公益基金会', " +
                    "'万行公益基金会致力于社区公益服务，专注于急救培训和AED设备捐赠，提升社区应急救护能力。我们的使命是让更多人掌握急救技能，在关键时刻能够挽救生命。', " +
                    "'张主任', '0755-12345678', 'default_logo.png', 1)";
                
                jdbcTemplate.update(orgSql);
                logger.info("成功插入组织数据");
            } catch (Exception e) {
                logger.warn("插入组织数据失败: {}", e.getMessage());
            }
            
            logger.info("数据初始化完成");
            
        } catch (Exception e) {
            logger.error("数据初始化过程中发生错误: {}", e.getMessage(), e);
        }
    }
}
