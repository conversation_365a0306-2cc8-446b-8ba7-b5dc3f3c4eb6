package com.example.demo.controller;

import com.example.demo.util.ImageUtil;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@CrossOrigin(origins = "*")
@RestController
@RequestMapping("/api/images")
public class ImageController {
    
    private static final Logger logger = LoggerFactory.getLogger(ImageController.class);
    
    /**
     * 获取PublicWelfare文件夹中的所有图片
     */
    @GetMapping("/public-welfare")
    public ResponseEntity<Map<String, Object>> getPublicWelfareImages() {
        try {
            List<String> images = ImageUtil.getPublicWelfareImages();
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("count", images.size());
            result.put("images", images);
            
            logger.info("获取PublicWelfare图片列表成功，共{}张图片", images.size());
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            logger.error("获取PublicWelfare图片列表失败: {}", e.getMessage(), e);
            
            Map<String, Object> error = new HashMap<>();
            error.put("success", false);
            error.put("error", "获取图片列表失败: " + e.getMessage());
            
            return ResponseEntity.status(500).body(error);
        }
    }
    
    /**
     * 验证图片是否存在
     */
    @GetMapping("/validate")
    public ResponseEntity<Map<String, Object>> validateImage(@RequestParam String path) {
        try {
            boolean exists = ImageUtil.isImageExists(path);
            String safePath = ImageUtil.getSafeImagePath(path);
            
            Map<String, Object> result = new HashMap<>();
            result.put("exists", exists);
            result.put("originalPath", path);
            result.put("safePath", safePath);
            result.put("isSupported", ImageUtil.isSupportedFormat(path));
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            logger.error("验证图片失败: {}", e.getMessage(), e);
            
            Map<String, Object> error = new HashMap<>();
            error.put("error", "验证图片失败: " + e.getMessage());
            
            return ResponseEntity.status(500).body(error);
        }
    }
}
