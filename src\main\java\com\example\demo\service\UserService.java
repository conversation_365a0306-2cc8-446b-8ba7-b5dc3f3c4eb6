package com.example.demo.service;

import com.example.demo.entity.User;
import java.util.List;

public interface UserService {
    boolean register(User user);
    User login(String username, String password);
    int update(User user);
    boolean updateUser(User user);
    int deleteById(Integer id);
    User getById(Integer id);
    User getUserById(Long id);
    List<User> getAll();

    // 新增方法
    boolean isUsernameExists(String username);
    boolean isEmailExists(String email);
    int getUserParticipationCount(Long userId);
}