package com.example.demo.service.impl;

import com.example.demo.entity.Activity;
import com.example.demo.entity.ActivityRegistration;
import com.example.demo.mapper.ActivityMapper;
import com.example.demo.mapper.ActivityRegistrationMapper;
import com.example.demo.service.ActivityService;
import com.example.demo.util.ImageUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.SimpleDateFormat;
import java.util.*;

@Service
public class ActivityServiceImpl implements ActivityService {

    private static final Logger logger = LoggerFactory.getLogger(ActivityServiceImpl.class);

    @Autowired
    private ActivityMapper activityMapper;

    @Autowired
    private ActivityRegistrationMapper registrationMapper;

    @Override
    public List<Activity> getAllActivities() {
        try {
            return activityMapper.findAll();
        } catch (Exception e) {
            logger.error("获取活动列表失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public Activity getActivityById(Long id) {
        try {
            return activityMapper.findById(id);
        } catch (Exception e) {
            logger.error("获取活动详情失败, ID: {}, 错误: {}", id, e.getMessage(), e);
            return null;
        }
    }

    @Override
    public List<Activity> getActivitiesByStatus(Integer status) {
        try {
            return activityMapper.findByStatus(status);
        } catch (Exception e) {
            logger.error("根据状态获取活动失败, status: {}, 错误: {}", status, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<Activity> searchActivities(String keyword) {
        try {
            if (keyword == null || keyword.trim().isEmpty()) {
                return getAllActivities();
            }
            return activityMapper.searchActivities(keyword.trim());
        } catch (Exception e) {
            logger.error("搜索活动失败, keyword: {}, 错误: {}", keyword, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<Activity> fullTextSearchActivities(String keyword) {
        try {
            if (keyword == null || keyword.trim().isEmpty()) {
                return getAllActivities();
            }
            // 尝试使用全文搜索，如果失败则回退到普通搜索
            try {
                return activityMapper.fullTextSearchActivities(keyword.trim());
            } catch (Exception fullTextError) {
                logger.warn("全文搜索失败，回退到普通搜索: {}", fullTextError.getMessage());
                return searchActivities(keyword);
            }
        } catch (Exception e) {
            logger.error("全文搜索活动失败, keyword: {}, 错误: {}", keyword, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<Activity> getActivitiesByCategory(String category) {
        try {
            if (category == null || category.trim().isEmpty()) {
                return getAllActivities();
            }
            return activityMapper.findByCategory(category.trim());
        } catch (Exception e) {
            logger.error("根据分类获取活动失败, category: {}, 错误: {}", category, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<Activity> getActivitiesByStatusAndCategory(Integer status, String category) {
        try {
            return activityMapper.findByStatusAndCategory(status, category);
        } catch (Exception e) {
            logger.error("根据状态和分类获取活动失败, status: {}, category: {}, 错误: {}", status, category, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    @Transactional
    public boolean createActivity(Activity activity) {
        try {
            // 设置默认值
            if (activity.getCurrentParticipants() == null) {
                activity.setCurrentParticipants(0);
            }
            if (activity.getStatus() == null) {
                activity.setStatus(0); // 默认为未开始
            }

            int result = activityMapper.insert(activity);
            logger.info("创建活动成功: {}", activity.getTitle());
            return result > 0;
        } catch (Exception e) {
            logger.error("创建活动失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean updateActivity(Activity activity) {
        try {
            int result = activityMapper.update(activity);
            logger.info("更新活动成功, ID: {}", activity.getId());
            return result > 0;
        } catch (Exception e) {
            logger.error("更新活动失败, ID: {}, 错误: {}", activity.getId(), e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean deleteActivity(Long id) {
        try {
            int result = activityMapper.deleteById(id);
            logger.info("删除活动成功, ID: {}", id);
            return result > 0;
        } catch (Exception e) {
            logger.error("删除活动失败, ID: {}, 错误: {}", id, e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean joinActivity(Long userId, Long activityId) {
        try {
            // 检查是否已经报名
            if (isUserRegistered(userId, activityId)) {
                logger.warn("用户已报名该活动, userId: {}, activityId: {}", userId, activityId);
                return false;
            }

            // 检查活动是否存在
            Activity activity = getActivityById(activityId);
            if (activity == null) {
                logger.warn("活动不存在, activityId: {}", activityId);
                return false;
            }

            // 检查是否还有名额
            if (activity.getCurrentParticipants() >= activity.getParticipantLimit()) {
                logger.warn("活动名额已满, activityId: {}", activityId);
                return false;
            }

            // 创建报名记录
            ActivityRegistration registration = new ActivityRegistration();
            registration.setUserId(userId);
            registration.setActivityId(activityId);
            registration.setStatus(1); // 1表示已报名

            int regResult = registrationMapper.insert(registration);
            if (regResult > 0) {
                // 增加活动参与人数
                int updateResult = activityMapper.incrementParticipants(activityId);
                logger.info("用户报名成功, userId: {}, activityId: {}", userId, activityId);
                return updateResult > 0;
            }

            return false;
        } catch (Exception e) {
            logger.error("用户报名失败, userId: {}, activityId: {}, 错误: {}", userId, activityId, e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean cancelRegistration(Long userId, Long activityId) {
        try {
            int result = registrationMapper.cancelRegistration(userId, activityId);
            if (result > 0) {
                // 减少活动参与人数
                activityMapper.decrementParticipants(activityId);
                logger.info("用户取消报名成功, userId: {}, activityId: {}", userId, activityId);
                return true;
            }
            return false;
        } catch (Exception e) {
            logger.error("用户取消报名失败, userId: {}, activityId: {}, 错误: {}", userId, activityId, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean isUserRegistered(Long userId, Long activityId) {
        try {
            return registrationMapper.checkUserRegistration(userId, activityId) > 0;
        } catch (Exception e) {
            logger.error("检查用户报名状态失败, userId: {}, activityId: {}, 错误: {}", userId, activityId, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public ActivityMapper.ActivityStatistics getActivityStatistics() {
        try {
            return activityMapper.getStatistics();
        } catch (Exception e) {
            logger.error("获取活动统计信息失败: {}", e.getMessage(), e);
            return new ActivityMapper.ActivityStatistics();
        }
    }

    @Override
    public List<Map<String, Object>> getUserActivities(Long userId) {
        try {
            List<ActivityRegistrationMapper.ActivityRegistrationWithDetails> registrations =
                registrationMapper.findByUserId(userId);

            List<Map<String, Object>> result = new ArrayList<>();
            for (ActivityRegistrationMapper.ActivityRegistrationWithDetails reg : registrations) {
                Map<String, Object> activity = new HashMap<>();
                activity.put("id", reg.getActivityId());
                activity.put("title", reg.getActivityTitle());
                activity.put("location", reg.getLocation());
                activity.put("startTime", reg.getStartTime());
                activity.put("endTime", reg.getEndTime());
                activity.put("registrationStatus", reg.getStatus());
                activity.put("registrationTime", reg.getCreateTime());
                result.add(activity);
            }

            return result;
        } catch (Exception e) {
            logger.error("获取用户活动列表失败, userId: {}, 错误: {}", userId, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public Map<String, Object> convertToFrontendFormat(Activity activity) {
        Map<String, Object> result = new HashMap<>();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");

        result.put("id", activity.getId());
        result.put("title", activity.getTitle());
        result.put("description", activity.getContent());

        // 使用ImageUtil处理图片路径，确保图片存在
        String imagePath = ImageUtil.getSafeImagePath(activity.getImage());
        result.put("image", imagePath);

        result.put("date", dateFormat.format(activity.getStartTime()));
        result.put("location", activity.getLocation());
        result.put("participants", activity.getCurrentParticipants());
        result.put("participantLimit", activity.getParticipantLimit());

        // 转换状态
        String status;
        switch (activity.getStatus()) {
            case 0: status = "upcoming"; break;
            case 1: status = "ongoing"; break;
            case 2: status = "completed"; break;
            default: status = "unknown"; break;
        }
        result.put("status", status);

        return result;
    }

    @Override
    public List<Map<String, Object>> convertListToFrontendFormat(List<Activity> activities) {
        List<Map<String, Object>> result = new ArrayList<>();
        for (Activity activity : activities) {
            result.add(convertToFrontendFormat(activity));
        }
        return result;
    }
}
