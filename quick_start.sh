#!/bin/bash

echo "========================================"
echo "社区公益服务平台 - 快速启动脚本"
echo "万行公益基金会版本"
echo "========================================"
echo

echo "[1/4] 检查MySQL服务状态..."
if command -v mysql &> /dev/null; then
    echo "✓ MySQL已安装"
else
    echo "✗ MySQL未安装，请先安装MySQL"
    exit 1
fi

echo
echo "[2/4] 初始化数据库..."
echo -n "请输入MySQL root密码: "
read -s mysql_password
echo

echo "正在执行数据库初始化脚本..."
mysql -u root -p$mysql_password < database_complete_init.sql
if [ $? -eq 0 ]; then
    echo "✓ 数据库初始化成功"
else
    echo "✗ 数据库初始化失败，请检查密码和MySQL连接"
    exit 1
fi

echo
echo "[3/4] 更新应用配置..."
echo "正在更新 application.properties 中的数据库密码..."

# 使用sed更新配置文件
sed -i.bak "s/spring.datasource.password=.*/spring.datasource.password=$mysql_password/" src/main/resources/application.properties

echo "✓ 配置文件更新完成"

echo
echo "[4/4] 启动应用..."
echo "正在编译和启动Spring Boot应用..."

# 检查是否有Maven
if command -v mvn &> /dev/null; then
    mvn spring-boot:run
else
    echo "✗ Maven未安装，请先安装Maven或使用IDE启动应用"
    exit 1
fi

echo
echo "========================================"
echo "🎉 启动完成！"
echo "========================================"
echo
echo "📱 访问地址："
echo "  首页: http://localhost:8090"
echo "  活动中心: http://localhost:8090/acivies.html"
echo
echo "👤 测试账号："
echo "  管理员: admin / admin123"
echo "  组织者: wanxing_org / wanxing123"
echo "  志愿者: volunteer1 / vol123"
echo
echo "📊 数据库信息："
echo "  数据库名: gy"
echo "  活动数量: 8个"
echo "  用户数量: 5个"
echo "  报名记录: 16条"
echo
echo "========================================"
