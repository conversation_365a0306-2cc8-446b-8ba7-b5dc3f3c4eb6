package com.example.demo.service;

import com.example.demo.entity.Activity;
import com.example.demo.mapper.ActivityMapper;
import java.util.List;
import java.util.Map;

public interface ActivityService {
    
    /**
     * 获取所有活动
     */
    List<Activity> getAllActivities();
    
    /**
     * 根据ID获取活动
     */
    Activity getActivityById(Long id);
    
    /**
     * 根据状态获取活动
     */
    List<Activity> getActivitiesByStatus(Integer status);
    
    /**
     * 搜索活动
     */
    List<Activity> searchActivities(String keyword);
    
    /**
     * 创建活动
     */
    boolean createActivity(Activity activity);
    
    /**
     * 更新活动
     */
    boolean updateActivity(Activity activity);
    
    /**
     * 删除活动
     */
    boolean deleteActivity(Long id);
    
    /**
     * 用户报名参加活动
     */
    boolean joinActivity(Long userId, Long activityId);
    
    /**
     * 用户取消报名
     */
    boolean cancelRegistration(Long userId, Long activityId);
    
    /**
     * 检查用户是否已报名
     */
    boolean isUserRegistered(Long userId, Long activityId);
    
    /**
     * 获取活动统计信息
     */
    ActivityMapper.ActivityStatistics getActivityStatistics();
    
    /**
     * 获取用户参与的活动列表
     */
    List<Map<String, Object>> getUserActivities(Long userId);
    
    /**
     * 将活动转换为前端需要的格式
     */
    Map<String, Object> convertToFrontendFormat(Activity activity);
    
    /**
     * 将活动列表转换为前端需要的格式
     */
    List<Map<String, Object>> convertListToFrontendFormat(List<Activity> activities);
}
