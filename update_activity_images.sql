-- 更新活动图片和内容为本地路径，让内容与图片更匹配
-- 执行前请确保图片文件存在于 src/main/resources/static/img/PublicWelfare/ 目录中

-- 1. 社区义诊活动
UPDATE activities SET
    title = '社区义诊活动',
    content = '联合专业医疗团队，为社区居民提供免费义诊服务。活动包括基础健康检查、血压血糖测量、健康咨询、急救知识普及等。我们邀请了心内科、内科、外科等多科室的专业医师，为居民提供专业的医疗建议和健康指导。',
    image = '/img/PublicWelfare/社区义诊活动.png',
    location = '长沙市岳麓区社区卫生服务中心'
WHERE id = 1;

-- 2. 爱心助老活动
UPDATE activities SET
    title = '爱心助老活动',
    content = '为社区独居老人和困难老人提供生活帮助和精神关怀。志愿者将上门为老人打扫卫生、购买生活用品、陪伴聊天，同时普及健康知识和安全防护常识，让老年人感受到社会的温暖和关爱。',
    image = '/img/PublicWelfare/爱心助老活动.png',
    location = '长沙市天心区养老服务中心'
WHERE id = 2;

-- 3. 社区清洁日
UPDATE activities SET
    title = '社区清洁日',
    content = '组织社区环境美化活动，号召居民共同参与社区清洁工作。活动内容包括清理小区垃圾、整理绿化带、清洁公共设施、宣传环保知识等。通过集体行动，提升社区环境质量，增强居民的环保意识和社区归属感。',
    image = '/img/PublicWelfare/社区清洁日.png',
    location = '长沙市雨花区环保公园'
WHERE id = 3;

-- 4. 环保宣传活动
UPDATE activities SET
    title = '环保宣传活动',
    content = '举办环保主题宣传活动，通过展板展示、知识讲座、互动游戏等形式，向社区居民普及环保知识，倡导绿色低碳生活方式。活动包括垃圾分类指导、节能减排宣传、环保手工制作等，让环保理念深入人心。',
    image = '/img/PublicWelfare/环保宣传活动.png',
    location = '长沙市开福区文化广场'
WHERE id = 4;

-- 5. 儿童阅读日
UPDATE activities SET
    title = '儿童阅读日',
    content = '儿童阅读推广活动，为社区儿童提供优质的阅读环境和丰富的图书资源。活动包括故事分享、亲子阅读、读书心得交流等环节。通过阅读活动培养孩子们的阅读兴趣，提高语言表达能力，促进儿童健康成长。',
    image = '/img/PublicWelfare/儿童阅读日.png',
    location = '湖南省图书馆'
WHERE id = 5;

-- 6. 专科义诊活动
UPDATE activities SET
    title = '专科义诊活动',
    content = '邀请眼科、口腔科、皮肤科等专科医生为社区居民提供专业医疗服务。活动提供免费的专科检查、疾病筛查、健康咨询等服务，帮助居民及早发现和预防疾病，提升社区整体健康水平。',
    image = '/img/PublicWelfare/社区义诊活动.png',
    location = '长沙市芙蓉区医疗中心'
WHERE id = 6;

-- 7. 老年人健康关怀
UPDATE activities SET
    title = '老年人健康关怀',
    content = '针对老年人群体开展的专项关怀活动，重点关注老年人的身心健康。活动包括健康体检、心理疏导、康复指导、营养咨询等服务。志愿者还将教授老年人使用智能设备，帮助他们更好地融入数字化生活。',
    image = '/img/PublicWelfare/爱心助老活动.png',
    location = '长沙市望城区老年活动中心'
WHERE id = 7;

-- 8. 环境卫生整治
UPDATE activities SET
    title = '环境卫生整治',
    content = '社区环境卫生整治活动，动员全体居民参与社区环境改善工作。活动重点清理卫生死角、整治乱堆乱放、美化公共空间。通过集体劳动，不仅改善了居住环境，还增进了邻里关系，营造了和谐的社区氛围。',
    image = '/img/PublicWelfare/社区清洁日.png',
    location = '长沙市岳麓山风景区'
WHERE id = 8;

-- 验证更新结果
SELECT id, title, location, image FROM activities;
