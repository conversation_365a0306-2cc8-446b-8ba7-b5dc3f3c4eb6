<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.demo.dao.UserDao">
    <!-- 结果映射 -->
    <resultMap id="UserResultMap" type="com.example.demo.entity.User">
        <id property="id" column="id"/>
        <result property="username" column="username"/>
        <result property="password" column="password"/>
        <result property="email" column="email"/>
        <result property="phone" column="phone"/>
        <result property="address" column="address"/>
        <result property="points" column="points"/>
        <result property="role" column="role_id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <!-- 查询所有字段 -->
    <sql id="Base_Column_List">
        id, username, password, email, phone, address, points, role_id, create_time, update_time
    </sql>

    <!-- 插入用户 -->
    <insert id="insert" parameterType="com.example.demo.entity.User" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO users (username, password, email, phone, address, points, role_id)
        VALUES (#{username}, #{password}, #{email}, #{phone}, #{address}, #{points}, #{role})
    </insert>

    <!-- 更新用户 -->
    <update id="update" parameterType="com.example.demo.entity.User">
        UPDATE users
        <set>
            <if test="password != null">password = #{password},</if>
            <if test="email != null">email = #{email},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="address != null">address = #{address},</if>
            <if test="points != null">points = #{points},</if>
            <if test="role != null">role_id = #{role},</if>
        </set>
        WHERE id = #{id}
    </update>

    <!-- 根据ID删除用户 -->
    <delete id="deleteById" parameterType="java.lang.Integer">
        DELETE FROM users WHERE id = #{id}
    </delete>

    <!-- 根据ID查询用户 -->
    <select id="selectById" resultMap="UserResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM users
        WHERE id = #{id}
    </select>

    <!-- 根据用户名查询用户 -->
    <select id="selectByUsername" resultMap="UserResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM users
        WHERE username = #{username}
    </select>

    <!-- 查询所有用户 -->
    <select id="selectAll" resultMap="UserResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM users
    </select>
</mapper> 