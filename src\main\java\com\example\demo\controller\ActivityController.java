package com.example.demo.controller;

import com.example.demo.entity.Activity;
import com.example.demo.service.ActivityService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.http.ResponseEntity;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

@RestController
@RequestMapping("/api/activities")
public class ActivityController {
    private static final Logger logger = LoggerFactory.getLogger(ActivityController.class);

    @Autowired
    private ActivityService activityService;

    @GetMapping
    public ResponseEntity<List<Map<String, Object>>> getAllActivities(
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) Integer status) {
        try {
            logger.info("获取活动列表, keyword: {}, status: {}", keyword, status);

            List<Activity> activities;

            // 根据参数获取活动
            if (keyword != null && !keyword.trim().isEmpty()) {
                activities = activityService.searchActivities(keyword);
            } else if (status != null) {
                activities = activityService.getActivitiesByStatus(status);
            } else {
                activities = activityService.getAllActivities();
            }

            // 转换为前端格式
            List<Map<String, Object>> result = activityService.convertListToFrontendFormat(activities);

            logger.info("成功获取 {} 个活动", result.size());
            return ResponseEntity.ok(result);

        } catch (Exception e) {
            logger.error("获取活动列表失败: {}", e.getMessage(), e);
            return ResponseEntity.status(500).body(new ArrayList<>());
        }
    }

    @GetMapping("/{id}")
    public ResponseEntity<Map<String, Object>> getActivityById(@PathVariable Long id) {
        try {
            logger.info("获取活动详情，ID: {}", id);

            Activity activity = activityService.getActivityById(id);
            if (activity == null) {
                logger.warn("活动不存在，ID: {}", id);
                return ResponseEntity.notFound().build();
            }

            Map<String, Object> result = activityService.convertToFrontendFormat(activity);
            return ResponseEntity.ok(result);

        } catch (Exception e) {
            logger.error("获取活动详情失败: {}", e.getMessage(), e);
            return ResponseEntity.status(500).body(new HashMap<>());
        }
    }

    @PostMapping("/{id}/join")
    public ResponseEntity<String> joinActivity(@PathVariable Long id, @RequestBody Map<String, Object> request) {
        try {
            Long userId = Long.valueOf(request.get("userId").toString());
            logger.info("用户 {} 报名参加活动 {}", userId, id);

            boolean success = activityService.joinActivity(userId, id);
            if (success) {
                logger.info("用户 {} 成功报名活动 {}", userId, id);
                return ResponseEntity.ok("报名成功");
            } else {
                logger.warn("用户 {} 报名活动 {} 失败", userId, id);
                return ResponseEntity.badRequest().body("报名失败，可能是名额已满或已经报名过");
            }

        } catch (Exception e) {
            logger.error("活动报名失败: {}", e.getMessage(), e);
            return ResponseEntity.status(500).body("报名失败: " + e.getMessage());
        }
    }

    @PostMapping
    public ResponseEntity<String> createActivity(@RequestBody Activity activity) {
        try {
            logger.info("创建新活动: {}", activity.getTitle());

            boolean success = activityService.createActivity(activity);
            if (success) {
                logger.info("成功创建活动: {}", activity.getTitle());
                return ResponseEntity.ok("活动创建成功");
            } else {
                logger.warn("创建活动失败: {}", activity.getTitle());
                return ResponseEntity.badRequest().body("活动创建失败");
            }

        } catch (Exception e) {
            logger.error("创建活动失败: {}", e.getMessage(), e);
            return ResponseEntity.status(500).body("创建活动失败: " + e.getMessage());
        }
    }

    @PutMapping("/{id}")
    public ResponseEntity<String> updateActivity(@PathVariable Long id, @RequestBody Activity activity) {
        try {
            logger.info("更新活动，ID: {}", id);

            activity.setId(id);
            boolean success = activityService.updateActivity(activity);
            if (success) {
                logger.info("成功更新活动: {}", id);
                return ResponseEntity.ok("活动更新成功");
            } else {
                logger.warn("更新活动失败: {}", id);
                return ResponseEntity.badRequest().body("活动更新失败");
            }

        } catch (Exception e) {
            logger.error("更新活动失败: {}", e.getMessage(), e);
            return ResponseEntity.status(500).body("更新活动失败: " + e.getMessage());
        }
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<String> deleteActivity(@PathVariable Long id) {
        try {
            logger.info("删除活动，ID: {}", id);

            boolean success = activityService.deleteActivity(id);
            if (success) {
                logger.info("成功删除活动: {}", id);
                return ResponseEntity.ok("活动删除成功");
            } else {
                logger.warn("删除活动失败: {}", id);
                return ResponseEntity.badRequest().body("活动删除失败");
            }

        } catch (Exception e) {
            logger.error("删除活动失败: {}", e.getMessage(), e);
            return ResponseEntity.status(500).body("删除活动失败: " + e.getMessage());
        }
    }

    /**
     * 获取活动统计信息
     */
    @GetMapping("/statistics")
    public ResponseEntity<Map<String, Object>> getStatistics() {
        try {
            logger.info("获取活动统计信息");

            var stats = activityService.getActivityStatistics();
            Map<String, Object> result = new HashMap<>();
            result.put("totalActivities", stats.getTotalActivities());
            result.put("upcomingActivities", stats.getUpcomingActivities());
            result.put("ongoingActivities", stats.getOngoingActivities());
            result.put("completedActivities", stats.getCompletedActivities());
            result.put("totalParticipants", stats.getTotalParticipants());

            return ResponseEntity.ok(result);

        } catch (Exception e) {
            logger.error("获取活动统计信息失败: {}", e.getMessage(), e);
            return ResponseEntity.status(500).body(new HashMap<>());
        }
    }

    /**
     * 取消报名
     */
    @DeleteMapping("/{id}/join")
    public ResponseEntity<String> cancelRegistration(@PathVariable Long id, @RequestBody Map<String, Object> request) {
        try {
            Long userId = Long.valueOf(request.get("userId").toString());
            logger.info("用户 {} 取消报名活动 {}", userId, id);

            boolean success = activityService.cancelRegistration(userId, id);
            if (success) {
                logger.info("用户 {} 成功取消报名活动 {}", userId, id);
                return ResponseEntity.ok("取消报名成功");
            } else {
                logger.warn("用户 {} 取消报名活动 {} 失败", userId, id);
                return ResponseEntity.badRequest().body("取消报名失败");
            }

        } catch (Exception e) {
            logger.error("取消报名失败: {}", e.getMessage(), e);
            return ResponseEntity.status(500).body("取消报名失败: " + e.getMessage());
        }
    }
}
