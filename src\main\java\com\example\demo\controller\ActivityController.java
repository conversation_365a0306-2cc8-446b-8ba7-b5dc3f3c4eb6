package com.example.demo.controller;

import com.example.demo.entity.Activity;
import org.springframework.web.bind.annotation.*;
import org.springframework.http.ResponseEntity;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.SimpleDateFormat;
import java.util.*;

@RestController
@RequestMapping("/api/activities")
public class ActivityController {
    private static final Logger logger = LoggerFactory.getLogger(ActivityController.class);
    
    @GetMapping
    public ResponseEntity<List<Map<String, Object>>> getAllActivities() {
        try {
            logger.info("获取活动列表");
            
            // 模拟活动数据
            List<Map<String, Object>> activities = new ArrayList<>();
            
            Map<String, Object> activity1 = new HashMap<>();
            activity1.put("id", 1);
            activity1.put("title", "社区环保行动日");
            activity1.put("date", "2025-06-05");
            activity1.put("location", "社区公园");
            activity1.put("description", "本次活动旨在清理社区公园垃圾，倡导环保理念，提高居民环保意识，共建绿色家园。欢迎广大居民积极参与。");
            activity1.put("status", "upcoming");
            activity1.put("image", "https://picsum.photos/600/400?random=1");
            activities.add(activity1);
            
            Map<String, Object> activity2 = new HashMap<>();
            activity2.put("id", 2);
            activity2.put("title", "关爱孤寡老人");
            activity2.put("date", "2025-05-30");
            activity2.put("location", "阳光敬老院");
            activity2.put("description", "我们将组织志愿者前往阳光敬老院，为老人们提供生活服务，陪他们聊天解闷，共度美好时光。");
            activity2.put("status", "ongoing");
            activity2.put("image", "https://picsum.photos/600/400?random=2");
            activities.add(activity2);
            
            Map<String, Object> activity3 = new HashMap<>();
            activity3.put("id", 3);
            activity3.put("title", "爱心图书捐赠");
            activity3.put("date", "2025-05-28");
            activity3.put("location", "社区图书馆");
            activity3.put("description", "为贫困地区儿童捐赠图书，帮助他们拓宽视野，增长知识。欢迎捐赠各类适合儿童阅读的书籍。");
            activity3.put("status", "completed");
            activity3.put("image", "https://picsum.photos/600/400?random=3");
            activities.add(activity3);
            
            logger.info("成功获取 {} 个活动", activities.size());
            return ResponseEntity.ok(activities);
            
        } catch (Exception e) {
            logger.error("获取活动列表失败: {}", e.getMessage(), e);
            return ResponseEntity.status(500).body(new ArrayList<>());
        }
    }
    
    @GetMapping("/{id}")
    public ResponseEntity<Map<String, Object>> getActivityById(@PathVariable Long id) {
        try {
            logger.info("获取活动详情，ID: {}", id);
            
            // 模拟根据ID获取活动详情
            Map<String, Object> activity = new HashMap<>();
            activity.put("id", id);
            activity.put("title", "活动详情 " + id);
            activity.put("date", "2025-06-05");
            activity.put("location", "社区公园");
            activity.put("description", "这是活动 " + id + " 的详细描述");
            activity.put("status", "upcoming");
            activity.put("image", "https://picsum.photos/600/400?random=" + id);
            
            return ResponseEntity.ok(activity);
            
        } catch (Exception e) {
            logger.error("获取活动详情失败: {}", e.getMessage(), e);
            return ResponseEntity.status(500).body(new HashMap<>());
        }
    }
    
    @PostMapping("/{id}/join")
    public ResponseEntity<String> joinActivity(@PathVariable Long id, @RequestBody Map<String, Object> request) {
        try {
            Long userId = Long.valueOf(request.get("userId").toString());
            logger.info("用户 {} 报名参加活动 {}", userId, id);
            
            // 模拟报名逻辑
            // 这里应该调用服务层方法来处理报名
            
            logger.info("用户 {} 成功报名活动 {}", userId, id);
            return ResponseEntity.ok("报名成功");
            
        } catch (Exception e) {
            logger.error("活动报名失败: {}", e.getMessage(), e);
            return ResponseEntity.status(500).body("报名失败: " + e.getMessage());
        }
    }
    
    @PostMapping
    public ResponseEntity<String> createActivity(@RequestBody Activity activity) {
        try {
            logger.info("创建新活动: {}", activity.getTitle());
            
            // 模拟创建活动逻辑
            // 这里应该调用服务层方法来创建活动
            
            logger.info("成功创建活动: {}", activity.getTitle());
            return ResponseEntity.ok("活动创建成功");
            
        } catch (Exception e) {
            logger.error("创建活动失败: {}", e.getMessage(), e);
            return ResponseEntity.status(500).body("创建活动失败: " + e.getMessage());
        }
    }
    
    @PutMapping("/{id}")
    public ResponseEntity<String> updateActivity(@PathVariable Long id, @RequestBody Activity activity) {
        try {
            logger.info("更新活动，ID: {}", id);
            
            // 模拟更新活动逻辑
            activity.setId(id);
            
            logger.info("成功更新活动: {}", id);
            return ResponseEntity.ok("活动更新成功");
            
        } catch (Exception e) {
            logger.error("更新活动失败: {}", e.getMessage(), e);
            return ResponseEntity.status(500).body("更新活动失败: " + e.getMessage());
        }
    }
    
    @DeleteMapping("/{id}")
    public ResponseEntity<String> deleteActivity(@PathVariable Long id) {
        try {
            logger.info("删除活动，ID: {}", id);
            
            // 模拟删除活动逻辑
            
            logger.info("成功删除活动: {}", id);
            return ResponseEntity.ok("活动删除成功");
            
        } catch (Exception e) {
            logger.error("删除活动失败: {}", e.getMessage(), e);
            return ResponseEntity.status(500).body("删除活动失败: " + e.getMessage());
        }
    }
}
