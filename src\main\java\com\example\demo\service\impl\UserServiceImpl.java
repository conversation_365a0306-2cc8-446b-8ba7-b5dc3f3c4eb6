package com.example.demo.service.impl;

import com.example.demo.dao.UserDao;
import com.example.demo.entity.User;
import com.example.demo.service.UserService;
import com.example.demo.mapper.ActivityRegistrationMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class UserServiceImpl implements UserService {
    @Autowired
    private UserDao userDao;

    @Autowired
    private ActivityRegistrationMapper registrationMapper;

    @Override
    public boolean register(User user) {
        if (userDao.selectByUsername(user.getUsername()) != null) {
            return false;
        }

        // 验证并处理用户数据
        validateAndProcessUserData(user);

        return userDao.insert(user) > 0;
    }

    @Override
    public User login(String username, String password) {
        User user = userDao.selectByUsername(username);
        if (user != null && user.getPassword().equals(password)) {
            return user;
        }
        return null;
    }

    @Override
    public int update(User user) {
        // 验证并处理用户数据
        validateAndProcessUserData(user);

        return userDao.update(user);
    }

    @Override
    public int deleteById(Integer id) {
        return userDao.deleteById(id);
    }

    @Override
    public User getById(Integer id) {
        return userDao.selectById(id);
    }

    @Override
    public List<User> getAll() {
        return userDao.selectAll();
    }

    @Override
    public boolean updateUser(User user) {
        try {
            validateAndProcessUserData(user);
            return update(user) > 0;
        } catch (Exception e) {
            return false;
        }
    }

    @Override
    public User getUserById(Long id) {
        try {
            return userDao.selectById(id.intValue());
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public boolean isUsernameExists(String username) {
        try {
            return userDao.selectByUsername(username) != null;
        } catch (Exception e) {
            return false;
        }
    }

    @Override
    public boolean isEmailExists(String email) {
        try {
            // 需要在UserDao中添加selectByEmail方法
            // 暂时返回false，后续可以完善
            return false;
        } catch (Exception e) {
            return false;
        }
    }

    @Override
    public int getUserParticipationCount(Long userId) {
        try {
            return registrationMapper.getUserParticipationCount(userId);
        } catch (Exception e) {
            return 0;
        }
    }

    /**
     * 验证并处理用户数据，确保所有字段都不超出数据库限制
     * @param user 用户对象
     */
    private void validateAndProcessUserData(User user) {
        // 用户名最大长度50
        if (user.getUsername() != null && user.getUsername().length() > 50) {
            user.setUsername(user.getUsername().substring(0, 50));
        }

        // 密码最大长度255
        if (user.getPassword() != null && user.getPassword().length() > 255) {
            user.setPassword(user.getPassword().substring(0, 255));
        }

        // 邮箱最大长度100
        if (user.getEmail() != null && user.getEmail().length() > 100) {
            user.setEmail(user.getEmail().substring(0, 100));
        }

        // 电话最大长度20
        if (user.getPhone() != null && user.getPhone().length() > 20) {
            user.setPhone(user.getPhone().substring(0, 20));
        }

        // 地址最大长度255
        if (user.getAddress() != null && user.getAddress().length() > 255) {
            user.setAddress(user.getAddress().substring(0, 255));
        }
    }
}