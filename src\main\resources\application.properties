spring.application.name=demo1
server.port=8087

spring.mvc.pathmatch.matching-strategy=ant_path_matcher

# Swagger
swagger.enabled=true

# MySQL
spring.datasource.url=***************************************************************************************************
spring.datasource.username=root
spring.datasource.password=1234
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# MyBatis
mybatis.mapper-locations=classpath:mapper/*.xml
mybatis.type-aliases-package=com.example.demo.entity
mybatis.configuration.map-underscore-to-camel-case=true