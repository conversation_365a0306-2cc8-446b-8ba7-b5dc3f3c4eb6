package com.example.demo.mapper;

import com.example.demo.entity.ActivityRegistration;
import org.apache.ibatis.annotations.*;
import java.util.List;

@Mapper
public interface ActivityRegistrationMapper {
    
    /**
     * 用户报名参加活动
     */
    @Insert("INSERT INTO activity_registrations (user_id, activity_id, status) VALUES (#{userId}, #{activityId}, #{status})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(ActivityRegistration registration);
    
    /**
     * 检查用户是否已报名某活动
     */
    @Select("SELECT COUNT(*) FROM activity_registrations WHERE user_id = #{userId} AND activity_id = #{activityId}")
    int checkUserRegistration(@Param("userId") Long userId, @Param("activityId") Long activityId);
    
    /**
     * 获取用户的所有报名记录
     */
    @Select("SELECT ar.*, a.title as activity_title, a.start_time, a.end_time, a.location " +
            "FROM activity_registrations ar " +
            "LEFT JOIN activities a ON ar.activity_id = a.id " +
            "WHERE ar.user_id = #{userId} ORDER BY ar.create_time DESC")
    @Results({
        @Result(property = "id", column = "id"),
        @Result(property = "userId", column = "user_id"),
        @Result(property = "activityId", column = "activity_id"),
        @Result(property = "status", column = "status"),
        @Result(property = "createTime", column = "create_time"),
        @Result(property = "activityTitle", column = "activity_title"),
        @Result(property = "startTime", column = "start_time"),
        @Result(property = "endTime", column = "end_time"),
        @Result(property = "location", column = "location")
    })
    List<ActivityRegistrationWithDetails> findByUserId(Long userId);
    
    /**
     * 获取某活动的所有报名记录
     */
    @Select("SELECT ar.*, u.username, u.email, u.phone " +
            "FROM activity_registrations ar " +
            "LEFT JOIN users u ON ar.user_id = u.id " +
            "WHERE ar.activity_id = #{activityId} ORDER BY ar.create_time DESC")
    @Results({
        @Result(property = "id", column = "id"),
        @Result(property = "userId", column = "user_id"),
        @Result(property = "activityId", column = "activity_id"),
        @Result(property = "status", column = "status"),
        @Result(property = "createTime", column = "create_time"),
        @Result(property = "username", column = "username"),
        @Result(property = "email", column = "email"),
        @Result(property = "phone", column = "phone")
    })
    List<ActivityRegistrationWithUser> findByActivityId(Long activityId);
    
    /**
     * 取消报名
     */
    @Delete("DELETE FROM activity_registrations WHERE user_id = #{userId} AND activity_id = #{activityId}")
    int cancelRegistration(@Param("userId") Long userId, @Param("activityId") Long activityId);
    
    /**
     * 更新报名状态
     */
    @Update("UPDATE activity_registrations SET status = #{status} WHERE id = #{id}")
    int updateStatus(@Param("id") Long id, @Param("status") Integer status);
    
    /**
     * 获取用户参与的活动数量
     */
    @Select("SELECT COUNT(*) FROM activity_registrations WHERE user_id = #{userId}")
    int getUserParticipationCount(Long userId);
    
    /**
     * 报名记录详情类（包含活动信息）
     */
    class ActivityRegistrationWithDetails extends ActivityRegistration {
        private String activityTitle;
        private java.util.Date startTime;
        private java.util.Date endTime;
        private String location;
        
        // getter和setter
        public String getActivityTitle() { return activityTitle; }
        public void setActivityTitle(String activityTitle) { this.activityTitle = activityTitle; }
        
        public java.util.Date getStartTime() { return startTime; }
        public void setStartTime(java.util.Date startTime) { this.startTime = startTime; }
        
        public java.util.Date getEndTime() { return endTime; }
        public void setEndTime(java.util.Date endTime) { this.endTime = endTime; }
        
        public String getLocation() { return location; }
        public void setLocation(String location) { this.location = location; }
    }
    
    /**
     * 报名记录类（包含用户信息）
     */
    class ActivityRegistrationWithUser extends ActivityRegistration {
        private String username;
        private String email;
        private String phone;
        
        // getter和setter
        public String getUsername() { return username; }
        public void setUsername(String username) { this.username = username; }
        
        public String getEmail() { return email; }
        public void setEmail(String email) { this.email = email; }
        
        public String getPhone() { return phone; }
        public void setPhone(String phone) { this.phone = phone; }
    }
}
