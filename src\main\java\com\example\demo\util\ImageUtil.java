package com.example.demo.util;

import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;

/**
 * 图片工具类
 * 用于处理本地图片路径和验证
 */
public class ImageUtil {
    
    private static final Logger logger = LoggerFactory.getLogger(ImageUtil.class);
    
    // 支持的图片格式
    private static final List<String> SUPPORTED_FORMATS = Arrays.asList(
        ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp"
    );
    
    // 默认图片路径
    private static final String DEFAULT_IMAGE = "/img/logo.png";
    
    /**
     * 验证图片路径是否存在
     * @param imagePath 图片路径
     * @return 是否存在
     */
    public static boolean isImageExists(String imagePath) {
        if (imagePath == null || imagePath.trim().isEmpty()) {
            return false;
        }
        
        try {
            // 移除开头的斜杠，因为ClassPathResource不需要
            String resourcePath = imagePath.startsWith("/") ? imagePath.substring(1) : imagePath;
            Resource resource = new ClassPathResource("static/" + resourcePath);
            return resource.exists();
        } catch (Exception e) {
            logger.warn("检查图片是否存在时发生错误: {}, 路径: {}", e.getMessage(), imagePath);
            return false;
        }
    }
    
    /**
     * 获取安全的图片路径
     * 如果图片不存在，返回默认图片
     * @param imagePath 原始图片路径
     * @return 安全的图片路径
     */
    public static String getSafeImagePath(String imagePath) {
        if (isImageExists(imagePath)) {
            return imagePath;
        }
        
        logger.warn("图片不存在，使用默认图片: {}", imagePath);
        return DEFAULT_IMAGE;
    }
    
    /**
     * 验证图片格式是否支持
     * @param imagePath 图片路径
     * @return 是否支持
     */
    public static boolean isSupportedFormat(String imagePath) {
        if (imagePath == null || imagePath.trim().isEmpty()) {
            return false;
        }
        
        String lowerPath = imagePath.toLowerCase();
        return SUPPORTED_FORMATS.stream().anyMatch(lowerPath::endsWith);
    }
    
    /**
     * 标准化图片路径
     * 确保路径以/开头，用于Web访问
     * @param imagePath 原始路径
     * @return 标准化后的路径
     */
    public static String normalizeImagePath(String imagePath) {
        if (imagePath == null || imagePath.trim().isEmpty()) {
            return DEFAULT_IMAGE;
        }
        
        String normalized = imagePath.trim();
        
        // 确保路径以/开头
        if (!normalized.startsWith("/")) {
            normalized = "/" + normalized;
        }
        
        // 替换反斜杠为正斜杠
        normalized = normalized.replace("\\", "/");
        
        return normalized;
    }
    
    /**
     * 获取PublicWelfare文件夹中的所有图片
     * @return 图片路径列表
     */
    public static List<String> getPublicWelfareImages() {
        try {
            Resource resource = new ClassPathResource("static/img/PublicWelfare");
            File directory = resource.getFile();
            
            if (directory.exists() && directory.isDirectory()) {
                return Arrays.stream(directory.listFiles())
                    .filter(file -> file.isFile() && isSupportedFormat(file.getName()))
                    .map(file -> "/img/PublicWelfare/" + file.getName())
                    .sorted()
                    .collect(java.util.stream.Collectors.toList());
            }
        } catch (IOException e) {
            logger.error("获取PublicWelfare图片列表失败: {}", e.getMessage());
        }
        
        return Arrays.asList();
    }
}
