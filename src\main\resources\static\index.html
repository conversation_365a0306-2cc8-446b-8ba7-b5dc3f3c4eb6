<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>社区公益服务平台</title>
    <script src="js/vue.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
    <!-- Element UI CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/element-ui@2.15.13/lib/theme-chalk/index.css">
    <!-- Swiper CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@6/swiper-bundle.min.css">
    <style>
        /* 全局样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
        }

        body {
            background: #ffffff;
            margin: 0;
            overflow-x: hidden;
            line-height: 1.6;
        }

        /* 加载动画 */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.9);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            transition: opacity 0.3s ease;
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #43a047;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 页面进入动画 */
        .fade-in {
            animation: fadeIn 0.6s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* 按钮基本样式 */
        a {
            text-decoration: none;
        }

        button, .btn, a.btn-join, .action-btn {
            display: inline-block;
            padding: 8px 16px;
            border-radius: 4px;
            border: none;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        button:hover, .btn:hover, a.btn-join:hover, .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        /* 导航栏样式 - 参考万行基金会风格 */
        .navbar {
            background: #ffffff;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            padding: 0 20px;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 100;
            transition: all 0.3s ease;
            border-bottom: 1px solid #e8e8e8;
        }

        .navbar-container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 60px;
        }

        .navbar-logo {
            font-size: 1.4rem;
            font-weight: 600;
            color: #2c5aa0;
            display: flex;
            align-items: center;
            letter-spacing: 1px;
            position: relative;
        }

        .navbar-logo span {
            color: #e74c3c;
            margin-left: 5px;
            font-weight: 400;
        }

        .navbar-menu {
            display: flex;
            list-style: none;
            margin: 0;
            padding: 0;
        }

        .navbar-item {
            position: relative;
        }

        .navbar-link {
            display: block;
            padding: 0 15px;
            line-height: 60px;
            color: #333;
            text-decoration: none;
            transition: color 0.3s ease;
            font-size: 15px;
        }

        .navbar-link:hover {
            color: #2c5aa0;
        }

        .navbar-link.active {
            color: #2c5aa0;
            font-weight: 600;
            border-bottom: 2px solid #2c5aa0;
        }

        .navbar-dropdown {
            display: none;
            position: absolute;
            top: 100%;
            left: 0;
            background: white;
            border-radius: 4px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            min-width: 160px;
            z-index: 101;
        }

        .navbar-dropdown-item {
            padding: 10px 15px;
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .navbar-dropdown-item:hover {
            background: #f5f7fa;
        }

        .navbar-item:hover .navbar-dropdown {
            display: block;
        }

        .navbar-user {
            display: flex;
            align-items: center;
        }

        .navbar-user-avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: #f0f2f5;
            margin-right: 10px;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .navbar-user-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .navbar-user-name {
            font-size: 14px;
            color: #333;
            margin-right: 15px;
        }

        .navbar-mobile {
            display: none;
            cursor: pointer;
        }

        .navbar-mobile-icon {
            width: 24px;
            height: 24px;
            display: flex;
            flex-direction: column;
            justify-content: space-around;
        }

        .navbar-mobile-icon span {
            width: 100%;
            height: 2px;
            background: #333;
            transition: all 0.3s ease;
        }

        .navbar-mobile-menu {
            display: none;
            position: absolute;
            top: 60px;
            left: 0;
            right: 0;
            background: white;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            z-index: 100;
        }

        .navbar-mobile-menu.active {
            display: block;
        }

        .navbar-mobile-item {
            padding: 15px 20px;
            border-bottom: 1px solid #f0f2f5;
        }

        .navbar-mobile-link {
            color: #333;
            text-decoration: none;
            display: block;
        }

        /* 响应式适配 */
        @media (max-width: 768px) {
            .navbar-menu {
                display: none;
            }

            .navbar-user {
                display: none;
            }

            .navbar-mobile {
                display: block;
            }
        }

        /* 主要内容容器 */
        .main-content {
            padding-top: 70px;
            min-height: 100vh;
            background: #ffffff;
        }

        /* 英雄区域 - 参考万行基金会风格 */
        .hero-banner {
            position: relative;
            width: 100%;
            height: 70vh;
            min-height: 500px;
            overflow: hidden;
            margin-bottom: 80px;
        }

        .hero-banner .swiper-container,
        .hero-banner .swiper-wrapper,
        .hero-banner .swiper-slide {
            width: 100%;
            height: 100%;
        }

        .hero-banner .swiper-slide {
            position: relative;
        }

        .hero-banner-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 8s ease;
        }

        .swiper-slide-active .hero-banner-image {
            transform: scale(1.1);
        }

        .hero-banner-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            width: 100%;
            max-width: 1000px;
            padding: 0 20px;
            z-index: 2;
            color: white;
        }

        .hero-banner-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(to bottom, rgba(0,0,0,0.2), rgba(0,0,0,0.6));
            z-index: 1;
        }

        .hero-banner-title {
            font-size: 4rem;
            font-weight: 700;
            margin-bottom: 20px;
            text-shadow: 1px 1px 4px rgba(0,0,0,0.5);
            opacity: 0;
            transform: translateY(30px);
            animation: fadeInUp 1s forwards;
            animation-delay: 0.5s;
        }

        .hero-banner-subtitle {
            font-size: 1.5rem;
            margin-bottom: 30px;
            opacity: 0;
            transform: translateY(30px);
            animation: fadeInUp 1s forwards;
            animation-delay: 0.8s;
            max-width: 700px;
            margin-left: auto;
            margin-right: auto;
            line-height: 1.6;
        }

        .hero-banner-buttons {
            opacity: 0;
            transform: translateY(30px);
            animation: fadeInUp 1s forwards;
            animation-delay: 1.1s;
        }

        @keyframes fadeInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .section-heading {
            text-align: center;
            margin-bottom: 50px;
        }

        .section-heading h2 {
            font-size: 2.2rem;
            color: #2c5aa0;
            margin-bottom: 15px;
            position: relative;
            display: inline-block;
            font-weight: 600;
        }

        .section-heading h2::after {
            content: '';
            position: absolute;
            bottom: -8px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: #e74c3c;
        }

        .section-heading p {
            color: #666;
            font-size: 1.1rem;
            max-width: 700px;
            margin: 0 auto;
        }

        .feature-cards {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 30px;
            margin-bottom: 60px;
        }

        .feature-card {
            background: white;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.4s ease;
            text-align: center;
            padding: 40px 30px;
            position: relative;
            cursor: pointer;
        }

        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(67,160,71,0.1), transparent);
            transition: left 0.5s ease;
        }

        .feature-card:hover::before {
            left: 100%;
        }

        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 40px rgba(67,160,71,0.2);
        }

        .feature-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .feature-icon i {
            font-size: 2.5rem;
            color: #2c5aa0;
        }

        .feature-title {
            font-size: 1.4rem;
            color: #333;
            margin-bottom: 15px;
        }

        .feature-desc {
            color: #666;
            margin-bottom: 20px;
            line-height: 1.6;
        }

        .counter-section {
            background: linear-gradient(135deg, #2c5aa0 0%, #1e3a8a 100%);
            color: white;
            padding: 80px 0;
            margin: 80px 0;
            text-align: center;
            position: relative;
        }

        .counter-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="1.5" fill="rgba(255,255,255,0.1)"/></svg>');
            opacity: 0.3;
        }

        .counters {
            display: flex;
            justify-content: space-around;
            max-width: 1200px;
            margin: 0 auto;
            flex-wrap: wrap;
            position: relative;
            z-index: 1;
        }

        .counter-item {
            padding: 0 20px;
            margin-bottom: 30px;
            position: relative;
        }

        .counter-number {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .counter-title {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .testimonial-section {
            padding: 60px 0;
            background: #f9f9f9;
        }

        .testimonial-container {
            max-width: 1000px;
            margin: 0 auto;
        }

        .testimonial-card {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.05);
            margin: 0 15px;
        }

        .testimonial-text {
            font-size: 1.1rem;
            line-height: 1.7;
            color: #555;
            margin-bottom: 20px;
            position: relative;
        }

        .testimonial-text::before {
            content: '"';
            font-size: 5rem;
            color: #E8F5E9;
            position: absolute;
            top: -40px;
            left: -20px;
            z-index: -1;
            font-family: serif;
        }

        .testimonial-author {
            display: flex;
            align-items: center;
        }

        .testimonial-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            overflow: hidden;
            margin-right: 15px;
        }

        .testimonial-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .testimonial-name {
            font-weight: 600;
            color: #333;
        }

        .testimonial-role {
            color: #777;
            font-size: 0.9rem;
        }

        .cta-section {
            background: url('https://picsum.photos/1920/600?random=10') center/cover no-repeat;
            position: relative;
            padding: 100px 0;
            text-align: center;
            margin: 80px 0 0;
        }

        .cta-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(67,160,71,0.8);
            z-index: 1;
        }

        .cta-content {
            position: relative;
            z-index: 2;
            max-width: 800px;
            margin: 0 auto;
            color: white;
        }

        .cta-title {
            font-size: 2.5rem;
            margin-bottom: 20px;
            text-shadow: 1px 1px 3px rgba(0,0,0,0.3);
        }

        .cta-text {
            font-size: 1.2rem;
            margin-bottom: 30px;
            opacity: 0.9;
        }

        .swiper-pagination-bullet {
            width: 12px;
            height: 12px;
            opacity: 0.7;
        }

        .swiper-pagination-bullet-active {
            background-color: #43a047;
            opacity: 1;
        }

        @media (max-width: 992px) {
            .feature-cards {
                grid-template-columns: repeat(2, 1fr);
            }

            .hero-banner-title {
                font-size: 3rem;
            }
        }

        @media (max-width: 768px) {
            .feature-cards {
                grid-template-columns: 1fr;
            }

            .hero-banner-title {
                font-size: 2.5rem;
            }

            .hero-banner {
                height: 80vh;
            }

            .counter-section {
                padding: 50px 0;
            }

            .counter-item {
                width: 50%;
            }
        }

        /* 活动卡片样式优化 */
        .activity-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }

        .activity-card {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            position: relative;
        }

        .activity-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 35px rgba(0,0,0,0.15);
        }

        .activity-image {
            height: 200px;
            overflow: hidden;
            position: relative;
        }

        .activity-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .activity-card:hover .activity-image img {
            transform: scale(1.05);
        }

        .activity-content {
            padding: 25px;
        }

        .activity-title {
            font-size: 1.3rem;
            color: #333;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }

        .activity-title i {
            margin-right: 8px;
            color: #43a047;
        }

        .activity-meta {
            display: flex;
            gap: 15px;
            margin-bottom: 15px;
            font-size: 0.9rem;
            color: #666;
        }

        .activity-meta span {
            display: flex;
            align-items: center;
        }

        .activity-meta i {
            margin-right: 5px;
            color: #43a047;
        }

        .activity-desc {
            color: #666;
            line-height: 1.6;
            margin-bottom: 20px;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .activity-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .activity-status {
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
        }

        .activity-status i {
            margin-right: 5px;
        }

        .status-upcoming {
            background: #e3f2fd;
            color: #1976d2;
        }

        .status-on-going {
            background: #e8f5e8;
            color: #43a047;
        }

        .status-completed {
            background: #f3e5f5;
            color: #7b1fa2;
        }

        .btn-join {
            padding: 8px 20px;
            background: #43a047;
            color: white;
            border-radius: 20px;
            text-decoration: none;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
        }

        .btn-join i {
            margin-right: 5px;
        }

        .btn-join:hover {
            background: #388e3c;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(67,160,71,0.3);
        }

        /* 覆盖Element UI 默认样式 - 万行基金会风格 */
        .el-button--primary {
            background-color: #2c5aa0;
            border-color: #2c5aa0;
        }

        .el-button--primary:hover, .el-button--primary:focus {
            background-color: #1e3a8a;
            border-color: #1e3a8a;
        }

        .el-button--success {
            background-color: #e74c3c;
            border-color: #e74c3c;
        }

        .el-button--success:hover, .el-button--success:focus {
            background-color: #c0392b;
            border-color: #c0392b;
        }

        /* 页脚样式 */
        .footer {
            background: #2c3e50;
            color: white;
            text-align: center;
            padding: 30px 0;
            margin-top: 60px;
        }

        .footer-copyright {
            font-size: 0.9rem;
            opacity: 0.8;
        }
    </style>
</head>
<body>
    <!-- 加载动画 -->
    <div id="loading" class="loading-overlay">
        <div class="loading-spinner"></div>
    </div>

    <div id="app" class="fade-in" style="opacity: 0;">
        <!-- 导航栏 -->
        <nav class="navbar">
            <div class="navbar-container">
                <a href="#" class="navbar-logo">
                    <i class="ri-heart-fill" style="color:#e74c3c;margin-right:8px;"></i>社区公益 <span>服务平台</span>
                </a>

                <!-- 桌面端菜单 -->
                <ul class="navbar-menu">
                    <li class="navbar-item">
                        <a href="#" class="navbar-link active"><i class="ri-home-4-fill"></i> 首页</a>
                    </li>
                    <li class="navbar-item">
                        <a href="/acivies.html" class="navbar-link"><i class="ri-calendar-event-fill"></i> 活动中心</a>
                    </li>
                    <li class="navbar-item">
                        <a href="#" class="navbar-link"><i class="ri-team-fill"></i> 志愿者招募</a>
                    </li>
                    <li class="navbar-item">
                        <a href="#" class="navbar-link"><i class="ri-organization-chart"></i> 公益组织</a>
                    </li>
                    <li class="navbar-item">
                        <a href="#" class="navbar-link"><i class="ri-information-fill"></i> 关于我们</a>
                    </li>
                </ul>

                <!-- 用户信息区域 -->
                <div class="navbar-user" v-if="user">
                    <div class="navbar-user-avatar">
                        <i class="ri-user-3-fill"></i>
                    </div>
                    <div class="navbar-user-name">
                        {{ user.username }}
                    </div>
                    <div class="navbar-item">
                        <a href="#" class="navbar-link">
                            <i class="ri-settings-3-fill"></i>
                        </a>
                        <div class="navbar-dropdown">
                            <div class="navbar-dropdown-item" @click="goProfile">
                                <i class="ri-profile-fill"></i> 个人资料
                            </div>
                            <div class="navbar-dropdown-item" v-if="user.role === 3">
                                <i class="ri-dashboard-fill"></i> 管理后台
                            </div>
                            <div class="navbar-dropdown-item" v-if="user.role === 2">
                                <i class="ri-organization-chart"></i> 组织管理
                            </div>
                            <div class="navbar-dropdown-item" v-if="user.role === 1 || user.role === 2">
                                <i class="ri-add-circle-fill"></i> 发布活动
                            </div>
                            <div class="navbar-dropdown-item" @click="logout">
                                <i class="ri-logout-box-r-line"></i> 退出登录
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 移动端菜单按钮 -->
                <div class="navbar-mobile" @click="toggleMobileMenu">
                    <div class="navbar-mobile-icon">
                        <span></span>
                        <span></span>
                        <span></span>
                    </div>
                </div>
            </div>
            <!-- 移动端菜单 -->
            <div class="navbar-mobile-menu" :class="{ 'active': mobileMenuActive }">
                <div class="navbar-mobile-item">
                    <a href="#" class="navbar-mobile-link"><i class="ri-home-4-fill"></i> 首页</a>
                </div>
                <div class="navbar-mobile-item">
                    <a href="/acivies.html" class="navbar-mobile-link"><i class="ri-calendar-event-fill"></i> 活动中心</a>
                </div>
                <div class="navbar-mobile-item">
                    <a href="#" class="navbar-mobile-link"><i class="ri-team-fill"></i> 志愿者招募</a>
                </div>
                <div class="navbar-mobile-item">
                    <a href="#" class="navbar-mobile-link"><i class="ri-organization-chart"></i> 公益组织</a>
                </div>
                <div class="navbar-mobile-item">
                    <a href="#" class="navbar-mobile-link"><i class="ri-information-fill"></i> 关于我们</a>
                </div>
                <div class="navbar-mobile-item" v-if="user">
                    <a href="#" class="navbar-mobile-link" @click="goProfile"><i class="ri-profile-fill"></i> 个人资料</a>
                </div>
                <div class="navbar-mobile-item" v-if="user && user.role === 3">
                    <a href="#" class="navbar-mobile-link"><i class="ri-dashboard-fill"></i> 管理后台</a>
                </div>
                <div class="navbar-mobile-item" v-if="user && user.role === 2">
                    <a href="#" class="navbar-mobile-link"><i class="ri-organization-chart"></i> 组织管理</a>
                </div>
                <div class="navbar-mobile-item" v-if="user && (user.role === 1 || user.role === 2)">
                    <a href="#" class="navbar-mobile-link"><i class="ri-add-circle-fill"></i> 发布活动</a>
                </div>
                <div class="navbar-mobile-item" v-if="user">
                    <a href="#" class="navbar-mobile-link" @click="logout"><i class="ri-logout-box-r-line"></i> 退出登录</a>
                </div>
                <div class="navbar-mobile-item" v-else>
                    <a href="#" class="navbar-mobile-link" @click="goLogin"><i class="ri-login-box-line"></i> 登录</a>
                </div>
                <div class="navbar-mobile-item" v-else>
                    <a href="#" class="navbar-mobile-link" @click="goRegister"><i class="ri-user-add-line"></i> 注册</a>
                </div>
            </div>
        </nav>

        <!-- 主要内容区域 -->
        <div class="main-content">
            <!-- Element UI 通知消息示例 -->
            <el-alert
                v-if="user"
                title="欢迎回来！"
                type="success"
                :closable="true"
                show-icon
                style="margin-bottom: 20px">
                <template #default>
                    <p>{{user.username}}，感谢您对社区公益的支持！您已累计参与 {{user.participatedActivities || 0}} 次活动。</p>
                </template>
            </el-alert>

            <!-- 未登录用户提示 -->
            <el-alert
                v-else
                title="加入我们的公益大家庭！"
                type="info"
                :closable="true"
                show-icon
                style="margin-bottom: 20px">
                <template #default>
                    <p>登录后可以参与更多公益活动，记录您的志愿服务时长。
                        <el-button type="text" @click="goLogin" style="padding: 0; margin-left: 5px;">立即登录</el-button>
                        或
                        <el-button type="text" @click="goRegister" style="padding: 0; margin-left: 5px;">注册账号</el-button>
                    </p>
                </template>
            </el-alert>

            <!-- Element UI 日期选择器示例 -->
            <el-row :gutter="20" style="margin-bottom: 20px" v-if="user && user.role >= 2">
                <el-col :span="24">
                    <el-card class="box-card">
                        <template #header>
                            <div class="card-header">
                                <span><i class="ri-calendar-check-fill"></i> 快速创建活动</span>
                            </div>
                        </template>
                        <el-form :inline="true" class="demo-form-inline">
                            <el-form-item label="活动名称">
                                <el-input placeholder="请输入活动名称" style="width: 200px"></el-input>
                            </el-form-item>
                            <el-form-item label="活动日期">
                                <el-date-picker
                                    type="daterange"
                                    range-separator="至"
                                    start-placeholder="开始日期"
                                    end-placeholder="结束日期">
                                </el-date-picker>
                            </el-form-item>
                            <el-form-item>
                                <el-button type="primary">创建活动</el-button>
                            </el-form-item>
                        </el-form>
                    </el-card>
                </el-col>
            </el-row>

            <!-- 全屏英雄轮播 -->
            <div class="hero-banner">
                <div class="swiper-container hero-swiper">
                    <div class="swiper-wrapper">
                        <div class="swiper-slide" v-for="(banner, index) in heroBanners" :key="'hero-'+index">
                            <div class="hero-banner-overlay"></div>
                            <img :src="banner.image" :alt="banner.title" class="hero-banner-image">
                            <div class="hero-banner-content">
                                <h1 class="hero-banner-title">{{banner.title}}</h1>
                                <p class="hero-banner-subtitle">{{banner.subtitle}}</p>
                                <div class="hero-banner-buttons">
                                    <el-button type="primary" size="large" round icon="el-icon-plus" @click="goToActivities">
                                        {{banner.buttonText}}
                                    </el-button>
                                    <el-button type="default" size="large" round icon="el-icon-info" plain>
                                        了解更多
                                    </el-button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="swiper-pagination"></div>
                    <div class="swiper-button-next" style="color: white"></div>
                    <div class="swiper-button-prev" style="color: white"></div>
                </div>
            </div>

            <!-- 特色功能区 -->
            <div class="container" style="max-width: 1200px; margin: 0 auto; padding: 0 20px;">
                <div class="section-heading">
                    <h2>我们的行动</h2>
                    <p>致力于社区公益服务，让每个人都能参与到美好社区的建设中来</p>
                </div>

                <div class="feature-cards">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="ri-heart-pulse-fill"></i>
                        </div>
                        <h3 class="feature-title">应急救护培训</h3>
                        <p class="feature-desc">开展CPR、AED使用等应急救护技能培训，提升社区居民的自救互救能力，守护生命安全。</p>
                        <el-button type="success" plain round>了解培训</el-button>
                    </div>

                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="ri-community-fill"></i>
                        </div>
                        <h3 class="feature-title">社区志愿服务</h3>
                        <p class="feature-desc">组织各类社区志愿服务活动，关爱弱势群体，传递温暖与爱心，共建和谐社区。</p>
                        <el-button type="success" plain round>参与服务</el-button>
                    </div>

                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="ri-graduation-cap-fill"></i>
                        </div>
                        <h3 class="feature-title">公益教育推广</h3>
                        <p class="feature-desc">通过教育培训和宣传推广，提高公众的公益意识和参与度，培养更多公益人才。</p>
                        <el-button type="success" plain round>了解更多</el-button>
                    </div>
                </div>
            </div>

            <!-- 数据统计区 -->
            <div class="counter-section">
                <div class="counters">
                    <div class="counter-item">
                        <div class="counter-number">{{statistics.totalActivities}}+</div>
                        <div class="counter-title">社区投放AED设备</div>
                    </div>
                    <div class="counter-item">
                        <div class="counter-number">{{statistics.totalVolunteers}}+</div>
                        <div class="counter-title">培训志愿者</div>
                    </div>
                    <div class="counter-item">
                        <div class="counter-number">{{statistics.totalServiceHours}}+</div>
                        <div class="counter-title">累计服务时长（小时）</div>
                    </div>
                    <div class="counter-item">
                        <div class="counter-number">{{statistics.satisfactionRate}}%</div>
                        <div class="counter-title">培训合格率</div>
                    </div>
                </div>
            </div>

            <!-- 活动列表 -->
            <div class="container" style="max-width: 1200px; margin: 0 auto; padding: 0 20px;">
                <div class="section-heading">
                    <h2>最新动态</h2>
                    <p>了解我们最新的公益活动和培训项目，一起参与到社区建设中来</p>
                </div>

                <!-- 活动列表内容保持不变 -->
                <div class="activity-list">
                    <div class="activity-card" v-for="activity in activities" :key="activity.id">
                        <div class="activity-image">
                            <img :src="activity.image" :alt="activity.title">
                        </div>
                        <div class="activity-content">
                            <h3 class="activity-title"><i class="ri-heart-fill"></i> {{activity.title}}</h3>
                            <div class="activity-meta">
                                <span><i class="ri-calendar-event-fill"></i> {{activity.date}}</span>
                                <span><i class="ri-map-pin-2-fill"></i> {{activity.location}}</span>
                            </div>
                            <p class="activity-desc">{{activity.description}}</p>
                            <div class="activity-actions">
                                <span class="activity-status"
                                    :class="{
                                        'status-upcoming': activity.status === 'upcoming',
                                        'status-on-going': activity.status === 'ongoing',
                                        'status-completed': activity.status === 'completed'
                                    }">
                                    <i :class="{
                                        'ri-timer-line': activity.status === 'upcoming',
                                        'ri-timer-flash-line': activity.status === 'ongoing',
                                        'ri-checkbox-circle-line': activity.status === 'completed'
                                    }"></i>
                                    {{activity.status === 'upcoming' ? '未开始' :
                                    activity.status === 'ongoing' ? '进行中' : '已结束'}}
                                </span>
                                <a href="javascript:void(0)" class="btn-join" @click="activity.status !== 'completed' ? joinActivity(activity.id) : showActivityDetail(activity.id)">
                                    <i :class="activity.status !== 'completed' ? 'ri-user-add-line' : 'ri-search-line'"></i>
                                    {{activity.status !== 'completed' ? '我要参加' : '查看详情'}}
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Element UI 分页器 -->
                <el-pagination
                    class="mt-4 text-center"
                    background
                    layout="prev, pager, next"
                    :total="50">
                </el-pagination>
            </div>

            <!-- 用户评价轮播 -->
            <div class="testimonial-section">
                <div class="container" style="max-width: 1200px; margin: 0 auto; padding: 0 20px;">
                    <div class="section-heading">
                        <h2>万行者心声</h2>
                        <p>听听我们的志愿者在急救培训和公益服务中的真实感受</p>
                    </div>

                    <div class="testimonial-container">
                        <div class="swiper-container testimonial-swiper">
                            <div class="swiper-wrapper">
                                <div class="swiper-slide" v-for="(testimonial, index) in testimonials" :key="'test-'+index">
                                    <div class="testimonial-card">
                                        <p class="testimonial-text">{{testimonial.text}}</p>
                                        <div class="testimonial-author">
                                            <div class="testimonial-avatar">
                                                <img :src="testimonial.avatar" alt="User Avatar">
                                            </div>
                                            <div>
                                                <div class="testimonial-name">{{testimonial.name}}</div>
                                                <div class="testimonial-role">{{testimonial.role}}</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="swiper-pagination"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 召唤行动区 -->
            <div class="cta-section">
                <div class="cta-overlay"></div>
                <div class="cta-content">
                    <h2 class="cta-title">万众聚大道，行者为天下</h2>
                    <p class="cta-text">学会急救技能，守护生命安全。加入万行公益，成为社区安全守护者</p>
                    <el-button type="primary" size="large" round icon="el-icon-right" style="padding: 12px 30px;">成为万行者</el-button>
                </div>
            </div>
        </div>

        <!-- 页脚 -->
        <footer class="footer">
            <div class="footer-copyright">
                &copy; 2025 社区公益服务平台 版权所有 | 万众聚大道，行者为天下
            </div>
        </footer>
    </div>

<script src="https://cdn.jsdelivr.net/npm/vue@2.6.14/dist/vue.js"></script>
<script src="https://cdn.jsdelivr.net/npm/element-ui@2.15.13/lib/index.js"></script>
<script src="https://cdn.jsdelivr.net/npm/vue-router@3.5.3/dist/vue-router.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/vuex@3.6.2/dist/vuex.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/swiper@6/swiper-bundle.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/vue-awesome-swiper@4.1.1/dist/vue-awesome-swiper.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/vee-validate@3.4.14/dist/vee-validate.full.min.js"></script>
<script>
// 使用Element UI
Vue.use(ELEMENT);
// 使用VeeValidate
Vue.use(VeeValidate);
// 使用Swiper
Vue.use(VueAwesomeSwiper);

// 创建状态管理
const store = new Vuex.Store({
  state: {
    user: null,
    activities: [],
    volunteers: [],
    organizations: [],
    bannerList: [
      {
        id: 1,
        title: '让社区更美好',
        image: 'https://picsum.photos/1200/400?random=1',
        link: '/acivies.html'
      },
      {
        id: 2,
        title: '加入志愿者队伍',
        image: 'https://picsum.photos/1200/400?random=2',
        link: '/volunteer.html'
      }
    ]
  },
  mutations: {
    setUser(state, user) {
      state.user = user;
      // 持久化用户状态
      if (user) {
        localStorage.setItem('user', JSON.stringify(user));
      } else {
        localStorage.removeItem('user');
      }
    },
    setActivities(state, activities) {
      state.activities = activities;
    },
    setVolunteers(state, volunteers) {
      state.volunteers = volunteers;
    },
    setOrganizations(state, organizations) {
      state.organizations = organizations;
    }
  },
  actions: {
    // 获取活动列表
    async fetchActivities({ commit }) {
      try {
        // 从后端API获取数据
        const response = await axios.get('/api/activities');
        console.log('成功获取活动数据:', response.data);
        commit('setActivities', response.data);
      } catch (error) {
        console.warn('无法连接到后端API，使用模拟数据', error);
        // 如果API调用失败，使用模拟数据
        const activities = [
          {
            id: 1,
            title: 'AED急救培训走进深圳湾创业广场',
            date: '2025-06-05',
            location: '深圳湾创业广场',
            description: '应深圳湾创业广场党委邀请，万行公益基金会举办急救知识培训活动，重点培训自动体外除颤器（AED）的使用能力。',
            status: 'upcoming',
            image: 'https://picsum.photos/600/400?random=1',
            participants: 25
          },
          {
            id: 2,
            title: '万众益心，救在身边 - 社区AED设备捐赠',
            date: '2025-05-30',
            location: '万科城小区',
            description: '为社区配置AED设备并开展急救知识培训，让更多人懂急救、会急救，提升社区应急救护能力。',
            status: 'ongoing',
            image: 'https://picsum.photos/600/400?random=2',
            participants: 18
          },
          {
            id: 3,
            title: '应急医疗救援培训导师师资班',
            date: '2025-05-28',
            location: '宝安区应急医疗救援培训中心',
            description: '培养专业的急救培训导师，提升志愿者的急救教学能力，为社区急救培训提供师资保障。',
            status: 'completed',
            image: 'https://picsum.photos/600/400?random=3',
            participants: 20
          }
        ];
        commit('setActivities', activities);
      }
    },
    // 登录操作
    async login({ commit }, credentials) {
      try {
        // 发送登录请求到后端API
        const response = await axios.post('/api/user/login', credentials);
        const user = response.data;
        commit('setUser', user);
        return user;
      } catch (error) {
        console.warn('登录API调用失败，使用模拟数据', error);
        // 如果API调用失败，使用模拟数据
        return new Promise((resolve) => {
          setTimeout(() => {
            const user = {
              id: 1,
              username: credentials.username,
              role: 1, // 1: 普通用户, 2: 组织者, 3: 管理员
              avatar: null
            };
            commit('setUser', user);
            resolve(user);
          }, 800);
        });
      }
    },
    // 登出操作
    logout({ commit }) {
      commit('setUser', null);
    }
  }
});

new Vue({
    el: '#app',
    store,
    data: {
        mobileMenuActive: false,
        loading: false,
        statistics: {
            totalActivities: 660,
            totalVolunteers: 1000,
            totalServiceHours: 8500,
            satisfactionRate: 96
        },
        bannerList: [
          {
            id: 1,
            title: '让社区更美好',
            image: 'https://picsum.photos/1200/400?random=1',
            link: '/acivies.html'
          },
          {
            id: 2,
            title: '加入志愿者队伍',
            image: 'https://picsum.photos/1200/400?random=2',
            link: '/volunteer.html'
          }
        ],
        heroBanners: [
            {
                title: "万众聚大道，行者为天下",
                subtitle: "致力于社区公益服务，关注弱势群体帮扶和应急救护能力建设",
                image: "https://picsum.photos/id/1035/1920/1080",
                buttonText: "了解我们"
            },
            {
                title: "应急救护，守护生命",
                subtitle: "普及CPR、AED等急救技能，提升社区应急救护水平，让更多人跑赢黄金四分钟",
                image: "https://picsum.photos/id/325/1920/1080",
                buttonText: "参与培训"
            },
            {
                title: "志愿服务，温暖社区",
                subtitle: "汇聚爱心力量，关爱弱势群体，共建美好和谐社区",
                image: "https://picsum.photos/id/291/1920/1080",
                buttonText: "成为志愿者"
            }
        ],
        testimonials: [
            {
                text: "参加万行公益的急救培训让我掌握了CPR和AED使用技能，感觉自己能在关键时刻救人一命，这种感觉特别有意义。培训很专业，老师很耐心！",
                name: "李明",
                role: "急救培训志愿者",
                avatar: "https://picsum.photos/id/64/200/200"
            },
            {
                text: "作为一名医学生，通过万行公益的平台参与急救培训推广，不仅提升了自己的专业技能，也为社区安全贡献了力量。非常有成就感！",
                name: "张华",
                role: "医学院学生志愿者",
                avatar: "https://picsum.photos/id/65/200/200"
            },
            {
                text: "退休后加入万行公益，参与社区AED设备维护和急救知识普及工作，让我的退休生活更加充实有意义。帮助别人，快乐自己！",
                name: "王芳",
                role: "退休志愿者",
                avatar: "https://picsum.photos/id/64/200/200"
            }
        ]
    },
    computed: {
        user() {
            return this.$store.state.user;
        },
        activities() {
            return this.$store.state.activities;
        }
    },
    async created() {
        // 显示加载动画
        this.loading = true;

        try {
            // 页面加载时检查是否已登录
            const userStr = localStorage.getItem('user');
            if (userStr) {
                try {
                    const userData = JSON.parse(userStr);
                    this.$store.commit('setUser', userData);
                } catch (e) {
                    console.error('解析用户信息失败', e);
                    localStorage.removeItem('user');
                }
            }

            // 导航栏滚动效果
            window.addEventListener('scroll', this.handleScroll);

            // 获取活动数据
            await this.$store.dispatch('fetchActivities');

        } catch (error) {
            console.error('页面初始化失败:', error);
            this.$message.error('页面加载失败，请刷新重试');
        } finally {
            // 隐藏加载动画
            this.loading = false;
            setTimeout(() => {
                const loadingEl = document.getElementById('loading');
                const appEl = document.getElementById('app');
                if (loadingEl) loadingEl.style.display = 'none';
                if (appEl) appEl.style.opacity = '1';
            }, 500);
        }
    },
    mounted() {
        // 初始化轮播图
        this.$nextTick(() => {
            new Swiper('.hero-swiper', {
                loop: true,
                autoplay: {
                    delay: 5000,
                    disableOnInteraction: false,
                },
                pagination: {
                    el: '.swiper-pagination',
                    clickable: true,
                },
                navigation: {
                    nextEl: '.swiper-button-next',
                    prevEl: '.swiper-button-prev',
                },
                effect: 'fade',
                fadeEffect: {
                    crossFade: true
                },
                speed: 1000,
            });

            new Swiper('.testimonial-swiper', {
                slidesPerView: 1,
                spaceBetween: 30,
                loop: true,
                autoplay: {
                    delay: 4000,
                    disableOnInteraction: false,
                },
                pagination: {
                    el: '.swiper-pagination',
                    clickable: true,
                },
                breakpoints: {
                    768: {
                        slidesPerView: 2,
                        spaceBetween: 20,
                    },
                    1024: {
                        slidesPerView: 2,
                        spaceBetween: 30,
                    },
                }
            });

            // 启动统计数据动态更新
            this.updateStatistics();
        });
    },
    methods: {
        goRegister() {
            window.location.href = 'register.html';
        },
        goLogin() {
            window.location.href = 'login.html';
        },
        goProfile() {
            window.location.href = 'profile.html';
        },
        logout() {
            this.$store.dispatch('logout');
            // 退出动画
            const navbar = document.querySelector('.navbar');
            navbar.classList.add('fade-out');
            setTimeout(() => {
                navbar.classList.remove('fade-out');
            }, 300);

            // 显示退出成功消息
            this.$message({
                message: '退出登录成功',
                type: 'success',
                duration: 2000
            });
        },
        toggleMobileMenu() {
            this.mobileMenuActive = !this.mobileMenuActive;

            // 汉堡菜单动画
            const icon = document.querySelector('.navbar-mobile-icon');
            if (this.mobileMenuActive) {
                icon.style.transform = 'rotate(90deg)';
                icon.querySelectorAll('span')[0].style.transform = 'rotate(45deg) translate(5px, 5px)';
                icon.querySelectorAll('span')[1].style.opacity = '0';
                icon.querySelectorAll('span')[2].style.transform = 'rotate(-45deg) translate(5px, -5px)';
            } else {
                icon.style.transform = 'rotate(0)';
                icon.querySelectorAll('span')[0].style.transform = 'rotate(0) translate(0, 0)';
                icon.querySelectorAll('span')[1].style.opacity = '1';
                icon.querySelectorAll('span')[2].style.transform = 'rotate(0) translate(0, 0)';
            }
        },
        handleScroll() {
            const navbar = document.querySelector('.navbar');
            if (window.scrollY > 50) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        },
        async joinActivity(activityId) {
            if (!this.user) {
                this.$confirm('您需要先登录才能参加活动, 是否前往登录页面?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    window.location.href = 'login.html';
                }).catch(() => {});
                return;
            }

            const loading = this.$loading({
                lock: true,
                text: '正在报名...',
                spinner: 'el-icon-loading',
                background: 'rgba(0, 0, 0, 0.7)'
            });

            try {
                // 尝试调用后端API
                await axios.post(`/api/activities/${activityId}/join`, {
                    userId: this.user.id
                });

                this.$message({
                    message: '报名成功！请关注活动开始时间',
                    type: 'success',
                    duration: 3000
                });

                // 刷新活动列表
                await this.$store.dispatch('fetchActivities');

            } catch (error) {
                console.warn('报名API调用失败，使用模拟响应', error);

                // 模拟成功响应
                await new Promise(resolve => setTimeout(resolve, 1000));
                this.$message({
                    message: '报名成功！请关注活动开始时间',
                    type: 'success',
                    duration: 3000
                });
            } finally {
                loading.close();
            }
        },
        showActivityDetail(activityId) {
            window.location.href = `activity-detail.html?id=${activityId}`;
        },
        goToActivities() {
            window.location.href = '/acivies.html';
        },
        // 动态更新统计数据
        updateStatistics() {
            // 模拟数据增长
            setInterval(() => {
                if (Math.random() > 0.7) { // 30%的概率更新数据
                    this.statistics.totalVolunteers += Math.floor(Math.random() * 3) + 1;
                    this.statistics.totalServiceHours += Math.floor(Math.random() * 10) + 1;

                    // 偶尔增加活动数量
                    if (Math.random() > 0.9) {
                        this.statistics.totalActivities += 1;
                    }
                }
            }, 5000); // 每5秒检查一次
        }
    }
});
</script>
</body>
</html>