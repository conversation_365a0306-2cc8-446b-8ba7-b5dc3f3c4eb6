<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录 - 万行公益基金会</title>
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="js/vue.js"></script>
    <script src="https://unpkg.com/element-ui/lib/index.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', <PERSON><PERSON>, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        /* 背景动画 */
        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" stop-color="%23ffffff" stop-opacity="0.1"/><stop offset="100%" stop-color="%23ffffff" stop-opacity="0"/></radialGradient></defs><circle cx="200" cy="200" r="100" fill="url(%23a)"/><circle cx="800" cy="300" r="150" fill="url(%23a)"/><circle cx="400" cy="700" r="120" fill="url(%23a)"/><circle cx="900" cy="800" r="80" fill="url(%23a)"/></svg>') no-repeat center center;
            background-size: cover;
            animation: float 20s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        .login-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
            width: 100%;
            max-width: 400px;
            position: relative;
            z-index: 1;
            animation: slideUp 0.6s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .logo-section {
            text-align: center;
            margin-bottom: 30px;
        }

        .logo {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #2c5aa0, #1e3d72);
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 15px;
            box-shadow: 0 10px 20px rgba(44, 90, 160, 0.3);
        }

        .logo i {
            color: white;
            font-size: 36px;
        }

        .title {
            color: #2c5aa0;
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .subtitle {
            color: #666;
            font-size: 14px;
        }

        .form-section {
            margin-top: 30px;
        }

        .el-form-item {
            margin-bottom: 25px;
        }

        .el-input {
            border-radius: 12px;
        }

        .el-input__inner {
            border: 2px solid #e8ecf0;
            border-radius: 12px;
            height: 50px;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .el-input__inner:focus {
            border-color: #2c5aa0;
            box-shadow: 0 0 0 3px rgba(44, 90, 160, 0.1);
        }

        .el-input__prefix {
            left: 15px;
            color: #999;
        }

        .login-btn {
            width: 100%;
            height: 50px;
            background: linear-gradient(135deg, #2c5aa0, #1e3d72);
            border: none;
            border-radius: 12px;
            color: white;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 20px;
        }

        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(44, 90, 160, 0.3);
        }

        .login-btn:active {
            transform: translateY(0);
        }

        .login-btn:disabled {
            background: #bbb;
            cursor: not-allowed;
            transform: none;
        }

        .form-footer {
            text-align: center;
            margin-top: 20px;
        }

        .form-links {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
            align-items: center;
        }

        .form-links a {
            color: #2c5aa0;
            text-decoration: none;
            font-size: 14px;
            transition: color 0.3s ease;
        }

        .form-links a:hover {
            color: #e74c3c;
        }

        .register-link {
            color: #666;
            font-size: 14px;
        }

        .register-link a {
            color: #2c5aa0;
            text-decoration: none;
            font-weight: bold;
        }

        .register-link a:hover {
            color: #e74c3c;
        }

        /* 响应式设计 */
        @media (max-width: 480px) {
            .login-container {
                margin: 20px;
                padding: 30px 25px;
            }

            .title {
                font-size: 24px;
            }
        }

        /* 成功动画 */
        .success-animation {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(44, 90, 160, 0.9);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            color: white;
        }

        .success-icon {
            font-size: 80px;
            margin-bottom: 20px;
            animation: bounce 0.6s ease-in-out;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-30px); }
            60% { transform: translateY(-15px); }
        }
    </style>
</head>
<body>
    <div id="app">
        <!-- 成功动画 -->
        <div v-if="showSuccess" class="success-animation">
            <div class="success-icon">
                <i class="fas fa-check-circle"></i>
            </div>
            <h2>登录成功！</h2>
            <p>正在跳转到首页...</p>
        </div>

        <div class="login-container">
            <!-- Logo和标题 -->
            <div class="logo-section">
                <div class="logo">
                    <i class="fas fa-heart"></i>
                </div>
                <h1 class="title">万行公益</h1>
                <p class="subtitle">让爱心传递，让生命更美好</p>
            </div>

            <!-- 登录表单 -->
            <div class="form-section">
                <el-form :model="loginForm" :rules="loginRules" ref="loginForm">
                    <el-form-item prop="username">
                        <el-input
                            v-model="loginForm.username"
                            placeholder="请输入用户名"
                            prefix-icon="el-icon-user"
                            size="large">
                        </el-input>
                    </el-form-item>

                    <el-form-item prop="password">
                        <el-input
                            v-model="loginForm.password"
                            type="password"
                            placeholder="请输入密码"
                            prefix-icon="el-icon-lock"
                            size="large"
                            show-password
                            @keyup.enter.native="handleLogin">
                        </el-input>
                    </el-form-item>

                    <div class="form-links">
                        <el-checkbox v-model="rememberMe">记住我</el-checkbox>
                        <a href="#" @click="showForgotPassword">忘记密码？</a>
                    </div>

                    <button type="button" class="login-btn" @click="handleLogin" :disabled="loginLoading">
                        <i v-if="loginLoading" class="el-icon-loading"></i>
                        {{ loginLoading ? '登录中...' : '登录' }}
                    </button>
                </el-form>

                <div class="form-footer">
                    <p class="register-link">
                        还没有账号？<a href="register.html">立即注册</a>
                    </p>
                </div>
            </div>
        </div>
    </div>
<script>
        new Vue({
            el: '#app',
            data() {
                return {
                    loginLoading: false,
                    showSuccess: false,
                    rememberMe: false,
                    loginForm: {
                        username: '',
                        password: ''
                    },
                    loginRules: {
                        username: [
                            { required: true, message: '请输入用户名', trigger: 'blur' },
                            { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' }
                        ],
                        password: [
                            { required: true, message: '请输入密码', trigger: 'blur' },
                            { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
                        ]
                    }
                }
            },
            mounted() {
                // 检查是否有记住的登录信息
                this.loadRememberedLogin();

                // 检查是否已经登录
                const user = localStorage.getItem('user');
                if (user) {
                    const returnUrl = new URLSearchParams(window.location.search).get('returnUrl');
                    window.location.href = returnUrl || 'index.html';
                }
            },
            methods: {
                async handleLogin() {
                    try {
                        const valid = await this.$refs.loginForm.validate();
                        if (!valid) return;

                        this.loginLoading = true;

                        const response = await axios.post('/api/user/login', {
                            username: this.loginForm.username,
                            password: this.loginForm.password
                        });

                        if (response.data.id) {
                            // 登录成功
                            this.$message.success('登录成功！');

                            // 保存用户信息
                            localStorage.setItem('user', JSON.stringify(response.data));

                            // 记住登录信息
                            if (this.rememberMe) {
                                localStorage.setItem('rememberedLogin', JSON.stringify({
                                    username: this.loginForm.username,
                                    rememberMe: true
                                }));
                            } else {
                                localStorage.removeItem('rememberedLogin');
                            }

                            // 显示成功动画
                            this.showSuccess = true;

                            // 延迟跳转，显示成功动画
                            setTimeout(() => {
                                const returnUrl = new URLSearchParams(window.location.search).get('returnUrl');
                                window.location.href = returnUrl || 'index.html';
                            }, 2000);
                        } else {
                            this.$message.error(response.data.error || '登录失败');
                        }
                    } catch (error) {
                        console.error('登录错误:', error);
                        if (error.response && error.response.data && error.response.data.error) {
                            this.$message.error(error.response.data.error);
                        } else {
                            this.$message.error('登录失败，请检查网络连接');
                        }
                    } finally {
                        this.loginLoading = false;
                    }
                },

                loadRememberedLogin() {
                    const remembered = localStorage.getItem('rememberedLogin');
                    if (remembered) {
                        const data = JSON.parse(remembered);
                        this.loginForm.username = data.username;
                        this.rememberMe = data.rememberMe;
                    }
                },

                showForgotPassword() {
                    this.$alert('请联系管理员重置密码：<EMAIL>', '忘记密码', {
                        confirmButtonText: '确定',
                        type: 'info'
                    });
                }
            }
        });
    </script>
</body>
</html>