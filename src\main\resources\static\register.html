<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>用户注册</title>
    <script src="js/vue.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #fffde7 0%, #e8f5e9 100%);
            margin: 0;
        }
        .logo {
            text-align: center;
            margin-top: 40px;
            margin-bottom: 10px;
        }
        .logo img {
            width: 60px;
            height: 60px;
        }
        .logo .slogan {
            font-size: 22px;
            color: #43a047;
            font-weight: bold;
            margin-top: 8px;
            letter-spacing: 2px;
        }
        .register-container {
            width: 420px;
            margin: 30px auto 0 auto;
            background: #fff;
            padding: 36px 32px 28px 32px;
            border-radius: 16px;
            box-shadow: 0 4px 24px rgba(67,160,71,0.10);
        }
        .register-container h2 {
            text-align: center;
            margin-bottom: 24px;
            color: #43a047;
            letter-spacing: 1px;
        }
        .form-group { margin-bottom: 18px; }
        .form-group label {
            display: block;
            margin-bottom: 6px;
            color: #388e3c;
            font-weight: bold;
        }
        .form-group input, .form-group select {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid #c8e6c9;
            border-radius: 6px;
            box-sizing: border-box;
            font-size: 15px;
            background: #f9fbe7;
            transition: border 0.2s;
        }
        .form-group input:focus, .form-group select:focus {
            border: 1.5px solid #43a047;
            outline: none;
            background: #fff;
        }
        .btn {
            width: 100%;
            padding: 12px;
            background: linear-gradient(90deg, #43a047 60%, #ff9800 100%);
            color: #fff;
            border: none;
            border-radius: 6px;
            font-size: 17px;
            font-weight: bold;
            cursor: pointer;
            box-shadow: 0 2px 8px rgba(255,152,0,0.08);
            transition: background 0.2s;
        }
        .btn:disabled {
            background: #bdbdbd;
        }
        .error {
            color: #d84315;
            font-size: 14px;
            margin-top: 10px;
            text-align: center;
        }
        .success {
            color: #43a047;
            font-size: 14px;
            margin-top: 10px;
            text-align: center;
        }
    </style>
</head>
<body>
<div class="logo">
    <img src="https://img.icons8.com/color/96/000000/charity.png" alt="公益LOGO">
    <div class="slogan">让爱传递</div>
</div>
<div id="app" class="register-container">
    <h2>用户注册</h2>
    <form @submit.prevent="register">
        <div class="form-group">
            <label>用户名</label>
            <input type="text" v-model="form.username" required maxlength="50">
        </div>
        <div class="form-group">
            <label>密码</label>
            <input type="password" v-model="form.password" required maxlength="50">
        </div>
        <div class="form-group">
            <label>手机号</label>
            <input type="text" v-model="form.phone" maxlength="20">
        </div>
        <div class="form-group">
            <label>邮箱</label>
            <input type="email" v-model="form.email" maxlength="100">
        </div>
        <button class="btn" type="submit" :disabled="loading">{{ loading ? '注册中...' : '注册' }}</button>
    </form>
    <div v-if="error" class="error">{{ error }}</div>
    <div v-if="success" class="success">{{ success }}</div>
</div>
<script>
new Vue({
    el: '#app',
    data: {
        form: {
            username: '',
            password: '',
            phone: '',
            email: ''
        },
        error: '',
        success: '',
        loading: false
    },
    methods: {
        register() {
            // 简单校验
            if (!this.form.username || !this.form.password) {
                this.error = '用户名和密码不能为空';
                return;
            }
            
            this.error = '';
            this.success = '';
            this.loading = true;
            
            // 使用axios发送请求到后端API
            axios.post('/api/user/register', this.form)
                .then(response => {
                    if (response.data === '注册成功') {
                        this.success = response.data;
                        // 注册成功后清空表单
                        this.form = {
                            username: '',
                            password: '',
                            phone: '',
                            email: ''
                        };
                        // 延迟跳转到登录页
                        setTimeout(() => {
                            window.location.href = 'login.html';
                        }, 2000);
                    } else if (response.data === '用户名已存在') {
                        this.error = '用户名已存在，请换一个用户名';
                    } else {
                        this.error = '注册失败，请稍后再试';
                    }
                })
                .catch(error => {
                    this.error = '注册失败，请稍后再试';
                    console.error('注册失败:', error);
                })
                .finally(() => {
                    this.loading = false;
                });
        }
    }
});
</script>
</body>
</html> 