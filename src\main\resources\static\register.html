<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>注册 - 万行公益基金会</title>
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="js/vue.js"></script>
    <script src="https://unpkg.com/element-ui/lib/index.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', <PERSON><PERSON>, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow-x: hidden;
            padding: 20px 0;
        }

        /* 背景动画 */
        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" stop-color="%23ffffff" stop-opacity="0.1"/><stop offset="100%" stop-color="%23ffffff" stop-opacity="0"/></radialGradient></defs><circle cx="200" cy="200" r="100" fill="url(%23a)"/><circle cx="800" cy="300" r="150" fill="url(%23a)"/><circle cx="400" cy="700" r="120" fill="url(%23a)"/><circle cx="900" cy="800" r="80" fill="url(%23a)"/></svg>') no-repeat center center;
            background-size: cover;
            animation: float 20s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        .register-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
            width: 100%;
            max-width: 450px;
            position: relative;
            z-index: 1;
            animation: slideUp 0.6s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .logo-section {
            text-align: center;
            margin-bottom: 30px;
        }

        .logo {
            width: 70px;
            height: 70px;
            background: linear-gradient(135deg, #2c5aa0, #1e3d72);
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 15px;
            box-shadow: 0 10px 20px rgba(44, 90, 160, 0.3);
        }

        .logo i {
            color: white;
            font-size: 32px;
        }

        .title {
            color: #2c5aa0;
            font-size: 26px;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .subtitle {
            color: #666;
            font-size: 14px;
        }

        .form-section {
            margin-top: 30px;
        }

        .el-form-item {
            margin-bottom: 20px;
        }

        .el-input {
            border-radius: 12px;
        }

        .el-input__inner {
            border: 2px solid #e8ecf0;
            border-radius: 12px;
            height: 45px;
            font-size: 15px;
            transition: all 0.3s ease;
        }

        .el-input__inner:focus {
            border-color: #2c5aa0;
            box-shadow: 0 0 0 3px rgba(44, 90, 160, 0.1);
        }

        .el-input__prefix {
            left: 15px;
            color: #999;
        }

        .register-btn {
            width: 100%;
            height: 45px;
            background: linear-gradient(135deg, #2c5aa0, #1e3d72);
            border: none;
            border-radius: 12px;
            color: white;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 20px;
        }

        .register-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(44, 90, 160, 0.3);
        }

        .register-btn:active {
            transform: translateY(0);
        }

        .register-btn:disabled {
            background: #bbb;
            cursor: not-allowed;
            transform: none;
        }

        .form-footer {
            text-align: center;
            margin-top: 20px;
        }

        .login-link {
            color: #666;
            font-size: 14px;
        }

        .login-link a {
            color: #2c5aa0;
            text-decoration: none;
            font-weight: bold;
        }

        .login-link a:hover {
            color: #e74c3c;
        }

        .agreement {
            font-size: 13px;
            color: #999;
            margin-bottom: 20px;
            line-height: 1.5;
        }

        .agreement a {
            color: #2c5aa0;
            text-decoration: none;
        }

        .agreement a:hover {
            color: #e74c3c;
        }

        /* 响应式设计 */
        @media (max-width: 480px) {
            .register-container {
                margin: 20px;
                padding: 30px 25px;
            }

            .title {
                font-size: 22px;
            }
        }

        /* 成功动画 */
        .success-animation {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(44, 90, 160, 0.9);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            color: white;
        }

        .success-icon {
            font-size: 80px;
            margin-bottom: 20px;
            animation: bounce 0.6s ease-in-out;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-30px); }
            60% { transform: translateY(-15px); }
        }

        /* 步骤指示器 */
        .steps {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
        }

        .step {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: #e8ecf0;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 10px;
            font-size: 14px;
            font-weight: bold;
            color: #999;
            position: relative;
        }

        .step.active {
            background: #2c5aa0;
            color: white;
        }

        .step.completed {
            background: #67c23a;
            color: white;
        }

        .step:not(:last-child)::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 100%;
            width: 20px;
            height: 2px;
            background: #e8ecf0;
            transform: translateY(-50%);
        }

        .step.completed:not(:last-child)::after {
            background: #67c23a;
        }
    </style>
</head>
<body>
    <div id="app">
        <!-- 成功动画 -->
        <div v-if="showSuccess" class="success-animation">
            <div class="success-icon">
                <i class="fas fa-check-circle"></i>
            </div>
            <h2>注册成功！</h2>
            <p>正在跳转到登录页面...</p>
        </div>

        <div class="register-container">
            <!-- Logo和标题 -->
            <div class="logo-section">
                <div class="logo">
                    <i class="fas fa-user-plus"></i>
                </div>
                <h1 class="title">加入万行公益</h1>
                <p class="subtitle">一起传递爱心，让生命更美好</p>
            </div>

            <!-- 步骤指示器 -->
            <div class="steps">
                <div class="step" :class="{ active: currentStep === 1, completed: currentStep > 1 }">1</div>
                <div class="step" :class="{ active: currentStep === 2, completed: currentStep > 2 }">2</div>
                <div class="step" :class="{ active: currentStep === 3 }">3</div>
            </div>

            <!-- 注册表单 -->
            <div class="form-section">
                <el-form :model="registerForm" :rules="registerRules" ref="registerForm">
                    <!-- 第一步：基本信息 -->
                    <div v-show="currentStep === 1">
                        <el-form-item prop="username">
                            <el-input
                                v-model="registerForm.username"
                                placeholder="请输入用户名"
                                prefix-icon="el-icon-user"
                                size="medium"
                                @blur="checkUsername">
                            </el-input>
                        </el-form-item>

                        <el-form-item prop="password">
                            <el-input
                                v-model="registerForm.password"
                                type="password"
                                placeholder="请输入密码"
                                prefix-icon="el-icon-lock"
                                size="medium"
                                show-password>
                            </el-input>
                        </el-form-item>

                        <el-form-item prop="confirmPassword">
                            <el-input
                                v-model="registerForm.confirmPassword"
                                type="password"
                                placeholder="请确认密码"
                                prefix-icon="el-icon-lock"
                                size="medium"
                                show-password>
                            </el-input>
                        </el-form-item>
                    </div>

                    <!-- 第二步：联系信息 -->
                    <div v-show="currentStep === 2">
                        <el-form-item prop="email">
                            <el-input
                                v-model="registerForm.email"
                                placeholder="请输入邮箱"
                                prefix-icon="el-icon-message"
                                size="medium"
                                @blur="checkEmail">
                            </el-input>
                        </el-form-item>

                        <el-form-item prop="phone">
                            <el-input
                                v-model="registerForm.phone"
                                placeholder="请输入手机号"
                                prefix-icon="el-icon-phone"
                                size="medium">
                            </el-input>
                        </el-form-item>

                        <el-form-item prop="address">
                            <el-input
                                v-model="registerForm.address"
                                placeholder="请输入地址（可选）"
                                prefix-icon="el-icon-location"
                                size="medium">
                            </el-input>
                        </el-form-item>
                    </div>

                    <!-- 第三步：确认信息 -->
                    <div v-show="currentStep === 3">
                        <div class="agreement">
                            <el-checkbox v-model="agreeTerms">
                                我已阅读并同意 <a href="#" @click="showTerms">《用户协议》</a> 和 <a href="#" @click="showPrivacy">《隐私政策》</a>
                            </el-checkbox>
                        </div>

                        <div class="agreement">
                            <el-checkbox v-model="subscribeNews">
                                订阅万行公益基金会的活动通知和新闻资讯
                            </el-checkbox>
                        </div>
                    </div>

                    <!-- 按钮组 -->
                    <div style="display: flex; gap: 10px;">
                        <button
                            v-if="currentStep > 1"
                            type="button"
                            class="register-btn"
                            style="background: #909399; flex: 1;"
                            @click="prevStep">
                            上一步
                        </button>

                        <button
                            v-if="currentStep < 3"
                            type="button"
                            class="register-btn"
                            style="flex: 2;"
                            @click="nextStep">
                            下一步
                        </button>

                        <button
                            v-if="currentStep === 3"
                            type="button"
                            class="register-btn"
                            @click="handleRegister"
                            :disabled="registerLoading || !agreeTerms">
                            <i v-if="registerLoading" class="el-icon-loading"></i>
                            {{ registerLoading ? '注册中...' : '完成注册' }}
                        </button>
                    </div>
                </el-form>

                <div class="form-footer">
                    <p class="login-link">
                        已有账号？<a href="login.html">立即登录</a>
                    </p>
                </div>
            </div>
        </div>
    </div>
<script>
        new Vue({
            el: '#app',
            data() {
                return {
                    currentStep: 1,
                    registerLoading: false,
                    showSuccess: false,
                    agreeTerms: false,
                    subscribeNews: true,
                    registerForm: {
                        username: '',
                        password: '',
                        confirmPassword: '',
                        email: '',
                        phone: '',
                        address: ''
                    },
                    registerRules: {
                        username: [
                            { required: true, message: '请输入用户名', trigger: 'blur' },
                            { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' },
                            { pattern: /^[a-zA-Z0-9_]+$/, message: '用户名只能包含字母、数字和下划线', trigger: 'blur' }
                        ],
                        password: [
                            { required: true, message: '请输入密码', trigger: 'blur' },
                            { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
                        ],
                        confirmPassword: [
                            { required: true, message: '请确认密码', trigger: 'blur' },
                            { validator: this.validateConfirmPassword, trigger: 'blur' }
                        ],
                        email: [
                            { required: true, message: '请输入邮箱', trigger: 'blur' },
                            { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
                        ],
                        phone: [
                            { required: true, message: '请输入手机号', trigger: 'blur' },
                            { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号格式', trigger: 'blur' }
                        ]
                    }
                }
            },
            methods: {
                validateConfirmPassword(rule, value, callback) {
                    if (value !== this.registerForm.password) {
                        callback(new Error('两次输入的密码不一致'));
                    } else {
                        callback();
                    }
                },

                async nextStep() {
                    try {
                        if (this.currentStep === 1) {
                            // 验证第一步的字段
                            await this.$refs.registerForm.validateField(['username', 'password', 'confirmPassword']);
                        } else if (this.currentStep === 2) {
                            // 验证第二步的字段
                            await this.$refs.registerForm.validateField(['email', 'phone']);
                        }

                        this.currentStep++;
                    } catch (error) {
                        console.log('验证失败:', error);
                    }
                },

                prevStep() {
                    this.currentStep--;
                },

                async checkUsername() {
                    if (!this.registerForm.username) return;

                    try {
                        const response = await axios.get(`/api/user/check-username?username=${this.registerForm.username}`);
                        if (!response.data.available) {
                            this.$message.warning('用户名已存在，请换一个用户名');
                        }
                    } catch (error) {
                        console.warn('检查用户名失败:', error);
                    }
                },

                async checkEmail() {
                    if (!this.registerForm.email) return;

                    try {
                        const response = await axios.get(`/api/user/check-email?email=${this.registerForm.email}`);
                        if (!response.data.available) {
                            this.$message.warning('邮箱已被注册，请使用其他邮箱');
                        }
                    } catch (error) {
                        console.warn('检查邮箱失败:', error);
                    }
                },

                async handleRegister() {
                    try {
                        const valid = await this.$refs.registerForm.validate();
                        if (!valid) return;

                        if (!this.agreeTerms) {
                            this.$message.error('请先同意用户协议和隐私政策');
                            return;
                        }

                        this.registerLoading = true;

                        const response = await axios.post('/api/user/register', {
                            username: this.registerForm.username,
                            password: this.registerForm.password,
                            email: this.registerForm.email,
                            phone: this.registerForm.phone,
                            address: this.registerForm.address
                        });

                        if (response.data.message === '注册成功') {
                            this.$message.success('注册成功！');
                            this.showSuccess = true;

                            // 延迟跳转到登录页面
                            setTimeout(() => {
                                window.location.href = 'login.html';
                            }, 2000);
                        } else {
                            this.$message.error(response.data.error || '注册失败');
                        }
                    } catch (error) {
                        console.error('注册错误:', error);
                        if (error.response && error.response.data && error.response.data.error) {
                            this.$message.error(error.response.data.error);
                        } else {
                            this.$message.error('注册失败，请检查网络连接');
                        }
                    } finally {
                        this.registerLoading = false;
                    }
                },

                showTerms() {
                    this.$alert(`
                        <h3>用户协议</h3>
                        <p>1. 用户应遵守国家法律法规，不得利用本平台从事违法活动。</p>
                        <p>2. 用户应真实填写个人信息，不得冒用他人身份。</p>
                        <p>3. 用户应积极参与公益活动，传递正能量。</p>
                        <p>4. 平台有权对违规用户进行处理。</p>
                        <p>5. 本协议的最终解释权归万行公益基金会所有。</p>
                    `, '用户协议', {
                        confirmButtonText: '我已阅读',
                        dangerouslyUseHTMLString: true
                    });
                },

                showPrivacy() {
                    this.$alert(`
                        <h3>隐私政策</h3>
                        <p>1. 我们承诺保护您的个人隐私信息。</p>
                        <p>2. 您的个人信息仅用于平台服务和活动通知。</p>
                        <p>3. 我们不会向第三方泄露您的个人信息。</p>
                        <p>4. 您有权查看、修改或删除个人信息。</p>
                        <p>5. 如有疑问，请联系我们：<EMAIL></p>
                    `, '隐私政策', {
                        confirmButtonText: '我已阅读',
                        dangerouslyUseHTMLString: true
                    });
                }
            }
        });
    </script>
</body>
</html>