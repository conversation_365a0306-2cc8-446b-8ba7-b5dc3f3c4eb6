<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>活动中心 - 社区公益服务平台</title>
    <script src="js/vue.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
    <!-- Element UI CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/element-ui@2.15.13/lib/theme-chalk/index.css">
    <!-- Swiper CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@6/swiper-bundle.min.css">
    <style>
        /* 全局样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
        }

        body {
            background: #ffffff;
            margin: 0;
            overflow-x: hidden;
            line-height: 1.6;
        }

        /* 加载动画 */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.9);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            transition: opacity 0.3s ease;
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #2c5aa0;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 页面进入动画 */
        .fade-in {
            animation: fadeIn 0.6s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* 按钮基本样式 */
        a {
            text-decoration: none;
        }

        button, .btn, a.btn-join, .action-btn {
            display: inline-block;
            padding: 8px 16px;
            border-radius: 4px;
            border: none;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        button:hover, .btn:hover, a.btn-join:hover, .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        /* 导航栏样式 - 参考万行基金会风格 */
        .navbar {
            background: #ffffff;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            padding: 0 20px;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 100;
            transition: all 0.3s ease;
            border-bottom: 1px solid #e8e8e8;
        }

        .navbar-container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 60px;
        }

        .navbar-logo {
            font-size: 1.4rem;
            font-weight: 600;
            color: #2c5aa0;
            display: flex;
            align-items: center;
            letter-spacing: 1px;
            position: relative;
        }

        .navbar-logo span {
            color: #e74c3c;
            margin-left: 5px;
            font-weight: 400;
        }

        .navbar-menu {
            display: flex;
            list-style: none;
            margin: 0;
            padding: 0;
        }

        .navbar-item {
            position: relative;
        }

        .navbar-link {
            display: block;
            padding: 0 15px;
            line-height: 60px;
            color: #333;
            text-decoration: none;
            transition: color 0.3s ease;
            font-size: 15px;
        }

        .navbar-link:hover {
            color: #2c5aa0;
        }

        .navbar-link.active {
            color: #2c5aa0;
            font-weight: 600;
            border-bottom: 2px solid #2c5aa0;
        }

        /* 登录注册按钮样式 */
        .navbar-auth {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .auth-link {
            padding: 8px 16px !important;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            border: 1px solid transparent;
        }

        .auth-link:hover {
            color: #2c5aa0 !important;
            background: rgba(44, 90, 160, 0.1);
        }

        .auth-link.register {
            background: #2c5aa0;
            color: white !important;
            border-color: #2c5aa0;
        }

        .auth-link.register:hover {
            background: #1e3d72;
            color: white !important;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(44, 90, 160, 0.3);
        }

        /* 主要内容容器 */
        .main-content {
            padding-top: 70px;
            min-height: 100vh;
            background: #ffffff;
        }

        /* 搜索和筛选区域 */
        .search-filter-section {
            background: #f8f9fa;
            padding: 40px 0;
            margin-bottom: 40px;
        }

        .search-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .search-title {
            text-align: center;
            margin-bottom: 30px;
        }

        .search-title h1 {
            font-size: 2.2rem;
            color: #2c5aa0;
            margin-bottom: 10px;
            font-weight: 600;
        }

        .search-title p {
            color: #666;
            font-size: 1.1rem;
        }

        /* 活动卡片样式优化 */
        .activity-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }

        .activity-card {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            position: relative;
        }

        .activity-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 35px rgba(0,0,0,0.15);
        }

        .activity-image {
            height: 200px;
            overflow: hidden;
            position: relative;
        }

        .activity-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .activity-card:hover .activity-image img {
            transform: scale(1.05);
        }

        .activity-content {
            padding: 25px;
        }

        .activity-title {
            font-size: 1.3rem;
            color: #333;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }

        .activity-title i {
            margin-right: 8px;
            color: #2c5aa0;
        }

        .activity-meta {
            display: flex;
            gap: 15px;
            margin-bottom: 15px;
            font-size: 0.9rem;
            color: #666;
        }

        .activity-meta span {
            display: flex;
            align-items: center;
        }

        .activity-meta i {
            margin-right: 5px;
            color: #2c5aa0;
        }

        .activity-desc {
            color: #666;
            line-height: 1.6;
            margin-bottom: 20px;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .activity-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .activity-status {
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
        }

        .activity-status i {
            margin-right: 5px;
        }

        .status-upcoming {
            background: #e3f2fd;
            color: #1976d2;
        }

        .status-on-going {
            background: #e8f5e8;
            color: #43a047;
        }

        .status-completed {
            background: #f3e5f5;
            color: #7b1fa2;
        }

        .btn-join {
            padding: 8px 20px;
            background: #2c5aa0;
            color: white;
            border-radius: 20px;
            text-decoration: none;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
        }

        .btn-join i {
            margin-right: 5px;
        }

        .btn-join:hover {
            background: #1e3a8a;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(44,90,160,0.3);
        }

        /* 覆盖Element UI 默认样式 - 万行基金会风格 */
        .el-button--primary {
            background-color: #2c5aa0;
            border-color: #2c5aa0;
        }

        .el-button--primary:hover, .el-button--primary:focus {
            background-color: #1e3a8a;
            border-color: #1e3a8a;
        }

        .el-button--success {
            background-color: #e74c3c;
            border-color: #e74c3c;
        }

        .el-button--success:hover, .el-button--success:focus {
            background-color: #c0392b;
            border-color: #c0392b;
        }

        /* 页脚样式 */
        .footer {
            background: #2c3e50;
            color: white;
            text-align: center;
            padding: 30px 0;
            margin-top: 60px;
        }

        .footer-copyright {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        /* 响应式调整 */
        @media (max-width: 768px) {
            .activity-list {
                grid-template-columns: 1fr;
            }

            .search-title h1 {
                font-size: 1.8rem;
            }

            .navbar-menu {
                display: none;
            }
        }
    </style>
</head>
<body>
    <!-- 加载动画 -->
    <div id="loading" class="loading-overlay">
        <div class="loading-spinner"></div>
    </div>

    <div id="app" class="fade-in" style="opacity: 0;">
        <!-- 导航栏 -->
        <nav class="navbar">
            <div class="navbar-container">
                <a href="#" class="navbar-logo">
                    <i class="ri-heart-fill" style="color:#e74c3c;margin-right:8px;"></i>社区公益 <span>服务平台</span>
                </a>

                <!-- 桌面端菜单 -->
                <ul class="navbar-menu">
                    <li class="navbar-item">
                        <a href="index.html" class="navbar-link"><i class="ri-home-4-fill"></i> 首页</a>
                    </li>
                    <li class="navbar-item">
                        <a href="acivies.html" class="navbar-link active"><i class="ri-calendar-event-fill"></i> 活动中心</a>
                    </li>
                    <li class="navbar-item">
                        <a href="#" class="navbar-link"><i class="ri-team-fill"></i> 志愿者招募</a>
                    </li>
                    <li class="navbar-item">
                        <a href="#" class="navbar-link"><i class="ri-organization-chart"></i> 公益组织</a>
                    </li>
                    <li class="navbar-item">
                        <a href="#" class="navbar-link"><i class="ri-information-fill"></i> 关于我们</a>
                    </li>
                </ul>

                <!-- 登录/注册按钮 -->
                <div class="navbar-auth" v-if="!user">
                    <a href="login.html" class="navbar-link auth-link">
                        <i class="ri-login-box-line"></i> 登录
                    </a>
                    <a href="register.html" class="navbar-link auth-link register">
                        <i class="ri-user-add-line"></i> 注册
                    </a>
                </div>

                <!-- 用户信息区域 -->
                <div class="navbar-user" v-if="user" style="display: flex; align-items: center;">
                    <div style="width: 36px; height: 36px; border-radius: 50%; background: #f0f2f5; margin-right: 10px; display: flex; align-items: center; justify-content: center;">
                        <i class="ri-user-3-fill" style="color: #666;"></i>
                    </div>
                    <span style="font-size: 14px; color: #333; margin-right: 15px;">{{ user.username }}</span>
                    <el-dropdown @command="handleUserCommand">
                        <span style="cursor: pointer; color: #2c5aa0;">
                            <i class="ri-settings-3-fill"></i>
                        </span>
                        <el-dropdown-menu slot="dropdown">
                            <el-dropdown-item command="profile">个人中心</el-dropdown-item>
                            <el-dropdown-item command="activities">我的活动</el-dropdown-item>
                            <el-dropdown-item command="logout" divided>退出登录</el-dropdown-item>
                        </el-dropdown-menu>
                    </el-dropdown>
                </div>
            </div>
        </nav>

        <!-- 主要内容区域 -->
        <div class="main-content">
            <!-- 搜索和筛选区域 -->
            <div class="search-filter-section">
                <div class="search-container">
                    <div class="search-title">
                        <h1>活动中心</h1>
                        <p>发现并参与万行公益的培训活动和志愿服务项目</p>
                    </div>

                    <!-- 搜索和筛选表单 -->
                    <el-row :gutter="20">
                        <el-col :span="24">
                            <el-card class="box-card">
                                <el-form :inline="true" class="search-form">
                                    <el-form-item label="活动搜索">
                                        <el-input
                                            v-model="searchKeyword"
                                            placeholder="请输入活动名称或关键词"
                                            style="width: 250px"
                                            clearable>
                                            <template slot="prepend">
                                                <i class="ri-search-line"></i>
                                            </template>
                                        </el-input>
                                    </el-form-item>
                                    <el-form-item label="活动状态">
                                        <el-select v-model="statusFilter" placeholder="请选择状态" style="width: 150px">
                                            <el-option label="全部状态" value=""></el-option>
                                            <el-option label="未开始" value="upcoming"></el-option>
                                            <el-option label="进行中" value="ongoing"></el-option>
                                            <el-option label="已结束" value="completed"></el-option>
                                        </el-select>
                                    </el-form-item>
                                    <el-form-item label="活动类型">
                                        <el-select v-model="typeFilter" placeholder="请选择类型" style="width: 150px">
                                            <el-option label="全部类型" value=""></el-option>
                                            <el-option label="急救培训" value="training"></el-option>
                                            <el-option label="设备捐赠" value="donation"></el-option>
                                            <el-option label="志愿服务" value="volunteer"></el-option>
                                        </el-select>
                                    </el-form-item>
                                    <el-form-item>
                                        <el-button type="primary" @click="searchActivities">
                                            <i class="ri-search-line"></i> 搜索
                                        </el-button>
                                        <el-button @click="resetSearch">
                                            <i class="ri-refresh-line"></i> 重置
                                        </el-button>
                                    </el-form-item>
                                </el-form>
                            </el-card>
                        </el-col>
                    </el-row>
                </div>
            </div>

        <!-- 活动列表区 -->
        <div class="container" style="max-width: 1200px; margin: 0 auto; padding: 0 20px;">
            <div class="section-heading" style="text-align: center; margin-bottom: 40px;">
                <h2 style="font-size: 2.2rem; color: #2c5aa0; margin-bottom: 15px; font-weight: 600; position: relative; display: inline-block;">最新活动</h2>
                <div style="content: ''; position: absolute; bottom: -8px; left: 50%; transform: translateX(-50%); width: 60px; height: 3px; background: #e74c3c;"></div>
                <p style="color: #666; font-size: 1.1rem; margin-top: 20px;">参与万行公益的培训和志愿服务活动，共建安全社区</p>
            </div>
            <el-alert
                v-if="loading"
                title="正在加载活动数据..."
                type="info"
                :closable="false">
            </el-alert>
            <div class="activity-list">
                <div class="activity-card" v-for="activity in activities" :key="activity.id">
                    <div class="activity-image">
                        <img :src="activity.image" :alt="activity.title">
                    </div>
                    <div class="activity-content">
                        <h3 class="activity-title"><i class="ri-heart-pulse-fill"></i> {{activity.title}}</h3>
                        <div class="activity-meta">
                            <span><i class="ri-calendar-event-fill"></i> {{activity.date}}</span>
                            <span><i class="ri-map-pin-2-fill"></i> {{activity.location}}</span>
                        </div>
                        <p class="activity-desc">{{activity.description}}</p>
                        <div class="activity-actions">
                            <span class="activity-status"
                                  :class="{
                                    'status-upcoming': activity.status === 'upcoming',
                                    'status-on-going': activity.status === 'ongoing',
                                    'status-completed': activity.status === 'completed'
                                  }">
                                <i :class="{
                                    'ri-timer-line': activity.status === 'upcoming',
                                    'ri-timer-flash-line': activity.status === 'ongoing',
                                    'ri-checkbox-circle-line': activity.status === 'completed'
                                }"></i>
                                {{activity.status === 'upcoming' ? '未开始' :
                                  activity.status === 'ongoing' ? '进行中' : '已结束'}}
                            </span>
                            <a href="javascript:void(0)" class="btn-join" @click="activity.status !== 'completed' ? joinActivity(activity.id) : showActivityDetail(activity.id)">
                                <i :class="activity.status !== 'completed' ? 'ri-user-add-line' : 'ri-search-line'"></i>
                                {{activity.status !== 'completed' ? '我要参加' : '查看详情'}}
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <el-pagination
                class="mt-4 text-center"
                background
                layout="prev, pager, next"
                :total="50">
            </el-pagination>
        </div>

        <!-- 用户评价轮播 -->
        <div class="testimonial-section">
            <div class="container" style="max-width: 1200px; margin: 0 auto; padding: 0 20px;">
                <div class="section-heading">
                    <h2>志愿者心声</h2>
                    <p>听听参与者们的真实感受和收获</p>
                </div>
                <div class="testimonial-container">
                    <div class="swiper-container testimonial-swiper">
                        <div class="swiper-wrapper">
                            <div class="swiper-slide" v-for="(testimonial, index) in testimonials" :key="'test-'+index">
                                <div class="testimonial-card">
                                    <p class="testimonial-text">{{testimonial.text}}</p>
                                    <div class="testimonial-author">
                                        <div class="testimonial-avatar">
                                            <img :src="testimonial.avatar" alt="User Avatar">
                                        </div>
                                        <div>
                                            <div class="testimonial-name">{{testimonial.name}}</div>
                                            <div class="testimonial-role">{{testimonial.role}}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="swiper-pagination"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 页脚 -->
        <footer class="footer">
            <div class="footer-copyright">
                &copy; 2025 社区公益服务平台 版权所有 | 万众聚大道，行者为天下
            </div>
        </footer>
    </div>

    <!-- Element UI JS -->
    <script src="https://cdn.jsdelivr.net/npm/element-ui@2.15.13/lib/index.js"></script>
    <!-- Swiper JS -->
    <script src="https://cdn.jsdelivr.net/npm/swiper@6/swiper-bundle.min.js"></script>
    <!-- Vuex -->
    <script src="https://cdn.jsdelivr.net/npm/vuex@3.6.2/dist/vuex.min.js"></script>
    <script>
    Vue.use(ELEMENT);

    const store = new Vuex.Store({
        state: {
            loading: false,
            activities: [],
            heroBanners: [
                {title: '参与公益，点亮希望', subtitle: '每一次参与都是对美好社会的贡献，让我们携手共建和谐社区', image: 'img/spread love/1747240773999.png', buttonText: '立即报名'},
                {title: '志愿服务 · 成就自我', subtitle: '通过志愿服务，不仅能帮助他人，也能获得个人成长与满足感', image: 'img/spread love/1747240790352.png', buttonText: '浏览活动'},
                {title: '公益无限 · 爱心无疆', subtitle: '从身边小事做起，传递温暖，共建美好家园', image: 'img/spread love/1747240813384.png', buttonText: '了解更多'}
            ],
            testimonials: [
                {text: '参与公益活动让我收获了友谊和成长，感谢平台提供的机会！', name: '李明', role: '环保志愿者', avatar: 'https://picsum.photos/id/64/200/200'},
                {text: '通过志愿服务，我学会了更多与人沟通和协作的能力。', name: '张华', role: '学生志愿者', avatar: 'https://picsum.photos/id/65/200/200'},
                {text: '退休后参与社区公益，让我的生活更加有意义和快乐。', name: '王芳', role: '退休教师', avatar: 'https://picsum.photos/id/64/200/200'}
            ]
        },
        mutations: {
            setLoading(state, val) { state.loading = val; },
            setActivities(state, activities) { state.activities = activities; }
        }
    });
    new Vue({
        el: '#app',
        store,
        data: {
            user: null,
            searchKeyword: '',
            statusFilter: '',
            typeFilter: '',
            currentPage: 1,
            pageSize: 6
        },
        computed: {
            loading() { return this.$store.state.loading; },
            activities() { return this.$store.state.activities; },
            filteredActivities() {
                let filtered = this.activities;

                // 关键词搜索
                if (this.searchKeyword) {
                    filtered = filtered.filter(activity =>
                        activity.title.toLowerCase().includes(this.searchKeyword.toLowerCase()) ||
                        activity.description.toLowerCase().includes(this.searchKeyword.toLowerCase())
                    );
                }

                // 状态筛选
                if (this.statusFilter) {
                    filtered = filtered.filter(activity => activity.status === this.statusFilter);
                }

                // 类型筛选（基于标题关键词）
                if (this.typeFilter) {
                    const typeKeywords = {
                        'training': ['培训', '讲座', '教学'],
                        'donation': ['捐赠', '设备', 'AED'],
                        'volunteer': ['志愿', '服务', '维护']
                    };
                    const keywords = typeKeywords[this.typeFilter] || [];
                    filtered = filtered.filter(activity =>
                        keywords.some(keyword => activity.title.includes(keyword))
                    );
                }

                return filtered;
            },
            totalActivities() {
                return this.filteredActivities.length;
            },
            totalPages() {
                return Math.ceil(this.totalActivities / this.pageSize);
            }
        },
        async created() {
            // 显示加载动画
            this.loading = true;

            try {
                // 检查是否已登录
                const userStr = localStorage.getItem('user');
                if (userStr) {
                    try {
                        const userData = JSON.parse(userStr);
                        this.user = userData;
                    } catch (e) {
                        console.error('解析用户信息失败', e);
                        localStorage.removeItem('user');
                    }
                }

                // 获取活动数据
                await this.loadActivities();
            } catch (error) {
                console.error('页面初始化失败:', error);
                this.$message.error('页面加载失败，请刷新重试');
            } finally {
                this.loading = false;
                // 隐藏加载动画
                setTimeout(() => {
                    const loadingEl = document.getElementById('loading');
                    const appEl = document.getElementById('app');
                    if (loadingEl) loadingEl.style.display = 'none';
                    if (appEl) appEl.style.opacity = '1';
                }, 500);
            }
        },
        mounted() {
            // 初始化轮播组件
            this.$nextTick(() => {
                if (document.querySelector('.testimonial-swiper')) {
                    new Swiper('.testimonial-swiper', {
                        slidesPerView: 1,
                        spaceBetween: 30,
                        loop: true,
                        autoplay: { delay: 4000, disableOnInteraction: false },
                        pagination: { el: '.swiper-pagination', clickable: true },
                        breakpoints: {
                            768: { slidesPerView: 2, spaceBetween: 20 },
                            1024: { slidesPerView: 2, spaceBetween: 30 }
                        }
                    });
                }
            });
        },
        methods: {
            // 加载活动数据
            async loadActivities() {
                try {
                    const response = await axios.get('/api/activities');
                    console.log('成功获取活动数据:', response.data);
                    this.$store.commit('setActivities', response.data);
                } catch (error) {
                    console.warn('无法连接到后端API，使用模拟数据', error);
                    // 如果API调用失败，使用模拟数据
                    const activities = [
                        {
                            id: 1,
                            title: 'AED急救培训走进深圳湾创业广场',
                            date: '2025-06-05',
                            location: '深圳湾创业广场',
                            description: '应深圳湾创业广场党委邀请，万行公益基金会举办急救知识培训活动，重点培训自动体外除颤器（AED）的使用能力。',
                            status: 'upcoming',
                            image: 'https://picsum.photos/600/400?random=1',
                            participants: 25
                        },
                        {
                            id: 2,
                            title: '万众益心，救在身边 - 社区AED设备捐赠',
                            date: '2025-05-30',
                            location: '万科城小区',
                            description: '为社区配置AED设备并开展急救知识培训，让更多人懂急救、会急救，提升社区应急救护能力。',
                            status: 'ongoing',
                            image: 'https://picsum.photos/600/400?random=2',
                            participants: 18
                        },
                        {
                            id: 3,
                            title: '应急医疗救援培训导师师资班',
                            date: '2025-05-28',
                            location: '宝安区应急医疗救援培训中心',
                            description: '培养专业的急救培训导师，提升志愿者的急救教学能力，为社区急救培训提供师资保障。',
                            status: 'completed',
                            image: 'https://picsum.photos/600/400?random=3',
                            participants: 20
                        }
                    ];
                    this.$store.commit('setActivities', activities);
                }
            },
            // 搜索活动
            searchActivities() {
                this.currentPage = 1; // 重置到第一页
                this.$message({
                    message: `找到 ${this.filteredActivities.length} 个相关活动`,
                    type: 'info'
                });
            },
            // 重置搜索
            resetSearch() {
                this.searchKeyword = '';
                this.statusFilter = '';
                this.typeFilter = '';
                this.currentPage = 1;
                this.$message({
                    message: '搜索条件已重置',
                    type: 'info'
                });
            },
            // 参加活动
            async joinActivity(id) {
                // 检查是否已登录
                const user = JSON.parse(localStorage.getItem('user') || 'null');
                if (!user) {
                    this.$confirm('您需要先登录才能参加活动, 是否前往登录页面?', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        window.location.href = 'login.html';
                    }).catch(() => {});
                    return;
                }

                const loading = this.$loading({
                    lock: true,
                    text: '正在报名...',
                    spinner: 'el-icon-loading',
                    background: 'rgba(0, 0, 0, 0.7)'
                });

                try {
                    // 调用后端API
                    const response = await axios.post(`/api/activities/${id}/join`, {
                        userId: user.id
                    });

                    this.$message({
                        message: '报名成功！请关注活动开始时间',
                        type: 'success',
                        duration: 3000
                    });

                    // 刷新活动列表
                    await this.loadActivities();

                } catch (error) {
                    console.warn('报名API调用失败', error);
                    if (error.response && error.response.status === 400) {
                        this.$message.error(error.response.data || '报名失败，可能是名额已满或已经报名过');
                    } else {
                        this.$message.error('报名失败，请稍后重试');
                    }
                } finally {
                    loading.close();
                }
            },
            // 查看活动详情
            showActivityDetail(id) {
                this.$message({
                    message: '活动详情页面开发中...',
                    type: 'info'
                });
            },
            // 获取状态文本
            getStatusText(status) {
                const statusMap = {
                    'upcoming': '未开始',
                    'ongoing': '进行中',
                    'completed': '已结束'
                };
                return statusMap[status] || '未知';
            },
            // 获取状态图标
            getStatusIcon(status) {
                const iconMap = {
                    'upcoming': 'ri-timer-line',
                    'ongoing': 'ri-timer-flash-line',
                    'completed': 'ri-checkbox-circle-line'
                };
                return iconMap[status] || 'ri-question-line';
            },
            // 分页处理
            handleCurrentChange(page) {
                this.currentPage = page;
                // 滚动到顶部
                window.scrollTo({ top: 0, behavior: 'smooth' });
            },

            // 用户下拉菜单处理
            handleUserCommand(command) {
                switch (command) {
                    case 'profile':
                        this.$message.info('个人中心功能开发中...');
                        break;
                    case 'activities':
                        this.$message.info('我的活动功能开发中...');
                        break;
                    case 'logout':
                        this.logout();
                        break;
                }
            },

            // 退出登录
            logout() {
                this.$confirm('确定要退出登录吗？', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    // 清除用户信息
                    this.user = null;
                    localStorage.removeItem('user');

                    this.$message.success('退出登录成功');

                    // 可选：跳转到首页
                    // window.location.href = 'index.html';
                }).catch(() => {});
            }
        }
    });
    </script>
</body>
</html>
