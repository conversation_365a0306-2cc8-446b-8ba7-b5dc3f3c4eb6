<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>公益活动列表 - 社区公益服务平台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/element-ui@2.15.13/lib/theme-chalk/index.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@6/swiper-bundle.min.css">
    <style>
        :root {
            --primary-color: #4CAF50;
            --secondary-color: #FF8C00;
            --light-green: #E8F5E9;
            --dark-green: #2E7D32;
        }
        
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            color: #333;
            background-color: #f9f9f9;
        }
        
        .navbar {
            background-color: #fff;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .navbar-brand {
            color: var(--primary-color);
            font-weight: bold;
            font-size: 1.5rem;
        }
        
        .nav-link {
            color: #555;
            font-weight: 500;
            margin: 0 10px;
            transition: all 0.3s;
        }
        
        .nav-link:hover {
            color: var(--primary-color);
        }
        
        .nav-link.active {
            color: var(--primary-color);
            border-bottom: 2px solid var(--primary-color);
        }
        
        .hero-section {
            background: linear-gradient(135deg, var(--light-green), #fff);
            padding: 3rem 0;
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .hero-section h1 {
            color: var(--dark-green);
            font-size: 2.5rem;
            margin-bottom: 1rem;
        }
        
        .hero-section p {
            color: #555;
            font-size: 1.1rem;
            max-width: 700px;
            margin: 0 auto;
        }
        
        .search-bar {
            background: white;
            padding: 1rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
            margin-bottom: 2rem;
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }
        
        .btn-primary:hover {
            background-color: var(--dark-green);
            border-color: var(--dark-green);
        }
        
        .btn-secondary {
            background-color: var(--secondary-color);
            border-color: var(--secondary-color);
        }
        
        .btn-secondary:hover {
            background-color: #E67E00;
            border-color: #E67E00;
        }
        
        .activity-card {
            background-color: #fff;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
            transition: transform 0.3s, box-shadow 0.3s;
            height: 100%;
            display: flex;
            flex-direction: column;
        }
        
        .activity-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
        
        .activity-image {
            width: 100%;
            height: 180px;
            object-fit: cover;
        }
        
        .activity-card .card-body {
            flex: 1;
            display: flex;
            flex-direction: column;
        }
        
        .activity-meta {
            color: #777;
            font-size: 0.9rem;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
        }
        
        .activity-meta i {
            margin-right: 5px;
            font-size: 1rem;
            color: var(--primary-color);
        }
        
        .participants-badge {
            background-color: var(--light-green);
            color: var(--dark-green);
            padding: 0.25rem 0.5rem;
            border-radius: 50px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        
        .card-footer {
            background-color: #fff;
            border-top: 1px solid #eee;
            padding: 0.75rem 1rem;
            margin-top: auto;
        }
        
        .activity-status {
            display: inline-block;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        
        .status-active {
            background-color: #E8F5E9;
            color: #2E7D32;
        }
        
        .status-ended {
            background-color: #FFEBEE;
            color: #C62828;
        }
        
        .status-upcoming {
            background-color: #E3F2FD;
            color: #1565C0;
        }
        
        .pagination .page-item.active .page-link {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }
        
        .pagination .page-link {
            color: var(--primary-color);
        }
        
        footer {
            background-color: #333;
            color: white;
            padding: 2rem 0;
            margin-top: 3rem;
        }
        
        footer a {
            color: #ddd;
            text-decoration: none;
        }
        
        footer a:hover {
            color: white;
            text-decoration: underline;
        }
        
        /* 响应式调整 */
        @media (max-width: 768px) {
            .hero-section {
                padding: 2rem 0;
            }
            .hero-section h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div id="app">
        <!-- 导航栏（可复用首页导航） -->
        <nav class="navbar navbar-expand-lg sticky-top">
            <div class="container">
                <a class="navbar-brand" href="/">
                    <i class="ri-heart-fill me-2"></i>社区公益服务平台
                </a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav ms-auto">
                        <li class="nav-item">
                            <a class="nav-link" href="/index.html"><i class="ri-home-4-line me-1"></i>首页</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="/acivies.html"><i class="ri-calendar-event-line me-1"></i>公益活动</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/volunteer.html"><i class="ri-user-heart-line me-1"></i>志愿者</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/organizations.html"><i class="ri-government-line me-1"></i>公益组织</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/news.html"><i class="ri-article-line me-1"></i>公益资讯</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/personal.html"><i class="ri-user-line me-1"></i>个人中心</a>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>

        <!-- 全屏轮播banner -->
        <div class="hero-banner">
            <div class="swiper-container hero-swiper">
                <div class="swiper-wrapper">
                    <div class="swiper-slide" v-for="(banner, index) in heroBanners" :key="'hero-'+index">
                        <div class="hero-banner-overlay"></div>
                        <img :src="banner.image" :alt="banner.title" class="hero-banner-image">
                        <div class="hero-banner-content">
                            <h1 class="hero-banner-title">{{banner.title}}</h1>
                            <p class="hero-banner-subtitle">{{banner.subtitle}}</p>
                            <div class="hero-banner-buttons">
                                <el-button type="primary" size="large" round icon="el-icon-plus" @click="goToActivities">
                                    {{banner.buttonText}}
                                </el-button>
                                <el-button type="default" size="large" round icon="el-icon-info" plain>
                                    了解更多
                                </el-button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="swiper-pagination"></div>
                <div class="swiper-button-next" style="color: white"></div>
                <div class="swiper-button-prev" style="color: white"></div>
            </div>
        </div>

        <!-- 特色功能区 -->
        <div class="container" style="max-width: 1200px; margin: 0 auto; padding: 0 20px;">
            <div class="section-heading">
                <h2>参与公益的理由</h2>
                <p>公益活动让我们凝聚爱心，服务社会，收获成长</p>
            </div>
            <div class="feature-cards">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="ri-hand-heart-line"></i>
                    </div>
                    <h3 class="feature-title">服务社会</h3>
                    <p class="feature-desc">通过参与公益活动，回馈社会，帮助有需要的人群。</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="ri-team-line"></i>
                    </div>
                    <h3 class="feature-title">结识伙伴</h3>
                    <p class="feature-desc">结交志同道合的朋友，拓展人脉，丰富人生阅历。</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="ri-medal-line"></i>
                    </div>
                    <h3 class="feature-title">自我提升</h3>
                    <p class="feature-desc">锻炼沟通、组织、协作等能力，提升自我价值。</p>
                </div>
            </div>
        </div>

        <!-- 数据统计区 -->
        <div class="counter-section">
            <div class="counters">
                <div class="counter-item">
                    <div class="counter-number">1,234+</div>
                    <div class="counter-title">累计活动</div>
                </div>
                <div class="counter-item">
                    <div class="counter-number">5,678+</div>
                    <div class="counter-title">志愿者人数</div>
                </div>
                <div class="counter-item">
                    <div class="counter-number">25,000+</div>
                    <div class="counter-title">服务时长（小时）</div>
                </div>
                <div class="counter-item">
                    <div class="counter-number">98%</div>
                    <div class="counter-title">满意度</div>
                </div>
            </div>
        </div>

        <!-- 活动列表区 -->
        <div class="container" style="max-width: 1200px; margin: 0 auto; padding: 0 20px;">
            <div class="section-heading">
                <h2>公益活动列表</h2>
                <p>发现并参与你感兴趣的公益活动</p>
            </div>
            <el-alert
                v-if="loading"
                title="正在加载活动数据..."
                type="info"
                :closable="false">
            </el-alert>
            <div class="activity-list">
                <div class="activity-card" v-for="activity in activities" :key="activity.id">
                    <div class="activity-image">
                        <img :src="activity.image" :alt="activity.title">
                    </div>
                    <div class="activity-content">
                        <h3 class="activity-title"><i class="ri-heart-fill"></i> {{activity.title}}</h3>
                        <div class="activity-meta">
                            <span><i class="ri-calendar-event-fill"></i> {{activity.date}}</span>
                            <span><i class="ri-map-pin-2-fill"></i> {{activity.location}}</span>
                        </div>
                        <p class="activity-desc">{{activity.description}}</p>
                        <div class="activity-actions">
                            <span class="activity-status" 
                                  :class="{
                                    'status-upcoming': activity.status === 'upcoming', 
                                    'status-on-going': activity.status === 'ongoing',
                                    'status-completed': activity.status === 'completed'
                                  }">
                                <i :class="{
                                    'ri-timer-line': activity.status === 'upcoming',
                                    'ri-timer-flash-line': activity.status === 'ongoing',
                                    'ri-checkbox-circle-line': activity.status === 'completed'
                                }"></i> 
                                {{activity.status === 'upcoming' ? '未开始' : 
                                  activity.status === 'ongoing' ? '进行中' : '已结束'}}
                            </span>
                            <a href="javascript:void(0)" class="btn-join" @click="activity.status !== 'completed' ? joinActivity(activity.id) : showActivityDetail(activity.id)">
                                <i :class="activity.status !== 'completed' ? 'ri-user-add-line' : 'ri-search-line'"></i> 
                                {{activity.status !== 'completed' ? '我要参加' : '查看详情'}}
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <el-pagination
                class="mt-4 text-center"
                background
                layout="prev, pager, next"
                :total="50">
            </el-pagination>
        </div>

        <!-- 用户评价轮播 -->
        <div class="testimonial-section">
            <div class="container" style="max-width: 1200px; margin: 0 auto; padding: 0 20px;">
                <div class="section-heading">
                    <h2>志愿者心声</h2>
                    <p>听听参与者们的真实感受和收获</p>
                </div>
                <div class="testimonial-container">
                    <div class="swiper-container testimonial-swiper">
                        <div class="swiper-wrapper">
                            <div class="swiper-slide" v-for="(testimonial, index) in testimonials" :key="'test-'+index">
                                <div class="testimonial-card">
                                    <p class="testimonial-text">{{testimonial.text}}</p>
                                    <div class="testimonial-author">
                                        <div class="testimonial-avatar">
                                            <img :src="testimonial.avatar" alt="User Avatar">
                                        </div>
                                        <div>
                                            <div class="testimonial-name">{{testimonial.name}}</div>
                                            <div class="testimonial-role">{{testimonial.role}}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="swiper-pagination"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 召唤行动区 -->
        <div class="cta-section">
            <div class="cta-overlay"></div>
            <div class="cta-content">
                <h2 class="cta-title">加入我们，共创美好社区</h2>
                <p class="cta-text">每一个小小的善举都能点亮他人的希望，成为志愿者，用行动传递爱与温暖</p>
                <el-button type="primary" size="large" round icon="el-icon-right" style="padding: 12px 30px;">立即加入</el-button>
            </div>
        </div>

        <!-- 页脚 -->
        <footer class="bg-dark text-light">
            <div class="container">
                <div class="row">
                    <div class="col-md-4 mb-4 mb-md-0">
                        <h5>关于我们</h5>
                        <p>社区公益服务平台致力于连接志愿者、公益组织和需要帮助的群体，共同建设美好社会。</p>
                    </div>
                    <div class="col-md-4 mb-4 mb-md-0">
                        <h5>联系方式</h5>
                        <ul class="list-unstyled">
                            <li><i class="ri-map-pin-line me-2"></i>地址：中国某省某市某区某街道1号</li>
                            <li><i class="ri-phone-line me-2"></i>电话：123-4567-8910</li>
                            <li><i class="ri-mail-line me-2"></i>邮箱：<EMAIL></li>
                        </ul>
                    </div>
                    <div class="col-md-4">
                        <h5>关注我们</h5>
                        <div class="d-flex">
                            <a href="#" class="me-3"><i class="ri-weibo-fill ri-xl"></i></a>
                            <a href="#" class="me-3"><i class="ri-wechat-fill ri-xl"></i></a>
                            <a href="#" class="me-3"><i class="ri-qq-fill ri-xl"></i></a>
                            <a href="#"><i class="ri-douyin-fill ri-xl"></i></a>
                        </div>
                    </div>
                </div>
                <hr class="my-4">
                <div class="text-center">
                    <p class="mb-0">© 2024 社区公益服务平台 版权所有</p>
                </div>
            </div>
        </footer>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/vue@2.6.14/dist/vue.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/element-ui@2.15.13/lib/index.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/vuex@3.6.2/dist/vuex.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/swiper@6/swiper-bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/vue-awesome-swiper@4.1.1/dist/vue-awesome-swiper.min.js"></script>
    <script>
    Vue.use(ELEMENT);
    Vue.use(VueAwesomeSwiper);
    const store = new Vuex.Store({
        state: {
            loading: false,
            activities: [
                {id: 1, title: '城市河道清洁日', date: '2024-07-20', location: '东湖公园北门', description: '组织志愿者清理城市河道垃圾，恢复河道生态环境，保护水资源。', status: 'ongoing', image: 'img/spread love/1747240385746.png'},
                {id: 2, title: '乡村爱心支教计划', date: '2024-08-15', location: '南山希望小学', description: '为乡村学校提供教育资源支持，开展兴趣课堂，丰富学生课余生活。', status: 'upcoming', image: 'img/spread love/1747240399177.png'},
                {id: 3, title: '社区爱心义卖活动', date: '2024-06-01', location: '中央广场', description: '组织社区爱心义卖，筹集善款帮助困难家庭和特殊儿童群体。', status: 'completed', image: 'img/spread love/1747240429358.png'}
            ],
            heroBanners: [
                {title: '参与公益，点亮希望', subtitle: '每一次参与都是对美好社会的贡献，让我们携手共建和谐社区', image: 'img/spread love/1747240773999.png', buttonText: '立即报名'},
                {title: '志愿服务 · 成就自我', subtitle: '通过志愿服务，不仅能帮助他人，也能获得个人成长与满足感', image: 'img/spread love/1747240790352.png', buttonText: '浏览活动'},
                {title: '公益无限 · 爱心无疆', subtitle: '从身边小事做起，传递温暖，共建美好家园', image: 'img/spread love/1747240813384.png', buttonText: '了解更多'}
            ],
            testimonials: [
                {text: '参与公益活动让我收获了友谊和成长，感谢平台提供的机会！', name: '李明', role: '环保志愿者', avatar: 'https://picsum.photos/id/64/200/200'},
                {text: '通过志愿服务，我学会了更多与人沟通和协作的能力。', name: '张华', role: '学生志愿者', avatar: 'https://picsum.photos/id/65/200/200'},
                {text: '退休后参与社区公益，让我的生活更加有意义和快乐。', name: '王芳', role: '退休教师', avatar: 'https://picsum.photos/id/64/200/200'}
            ]
        },
        mutations: {
            setLoading(state, val) { state.loading = val; }
        }
    });
    new Vue({
        el: '#app',
        store,
        computed: {
            loading() { return this.$store.state.loading; },
            activities() { return this.$store.state.activities; },
            heroBanners() { return this.$store.state.heroBanners; },
            testimonials() { return this.$store.state.testimonials; }
        },
        mounted() {
            this.$nextTick(() => {
                new Swiper('.hero-swiper', {
                    loop: true,
                    autoplay: { delay: 5000, disableOnInteraction: false },
                    pagination: { el: '.swiper-pagination', clickable: true },
                    navigation: { nextEl: '.swiper-button-next', prevEl: '.swiper-button-prev' },
                    effect: 'fade',
                    fadeEffect: { crossFade: true },
                    speed: 1000
                });
                new Swiper('.testimonial-swiper', {
                    slidesPerView: 1,
                    spaceBetween: 30,
                    loop: true,
                    autoplay: { delay: 4000, disableOnInteraction: false },
                    pagination: { el: '.swiper-pagination', clickable: true },
                    breakpoints: { 768: { slidesPerView: 2, spaceBetween: 20 }, 1024: { slidesPerView: 2, spaceBetween: 30 } }
                });
            });
        },
        methods: {
            joinActivity(id) {
                this.$message({ message: '报名成功！请关注活动开始时间', type: 'success' });
            },
            showActivityDetail(id) {
                window.location.href = `activity-detail.html?id=${id}`;
            },
            goToActivities() {
                window.location.href = '/acivies.html';
            }
        }
    });
    </script>
</body>
</html>
