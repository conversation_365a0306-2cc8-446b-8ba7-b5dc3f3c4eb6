package com.example.demo.mapper;

import com.example.demo.entity.Activity;
import org.apache.ibatis.annotations.*;
import java.util.List;

@Mapper
public interface ActivityMapper {
    
    /**
     * 获取所有活动
     */
    @Select("SELECT * FROM activities ORDER BY create_time DESC")
    List<Activity> findAll();
    
    /**
     * 根据ID获取活动
     */
    @Select("SELECT * FROM activities WHERE id = #{id}")
    Activity findById(Long id);
    
    /**
     * 根据状态获取活动
     */
    @Select("SELECT * FROM activities WHERE status = #{status} ORDER BY create_time DESC")
    List<Activity> findByStatus(Integer status);
    
    /**
     * 搜索活动（根据标题或内容）
     */
    @Select("SELECT * FROM activities WHERE title LIKE CONCAT('%', #{keyword}, '%') OR content LIKE CONCAT('%', #{keyword}, '%') ORDER BY create_time DESC")
    List<Activity> searchActivities(String keyword);
    
    /**
     * 根据组织者ID获取活动
     */
    @Select("SELECT * FROM activities WHERE organizer_id = #{organizerId} ORDER BY create_time DESC")
    List<Activity> findByOrganizerId(Long organizerId);
    
    /**
     * 创建新活动
     */
    @Insert("INSERT INTO activities (title, content, image, start_time, end_time, location, participant_limit, current_participants, status, organizer_id) " +
            "VALUES (#{title}, #{content}, #{image}, #{startTime}, #{endTime}, #{location}, #{participantLimit}, #{currentParticipants}, #{status}, #{organizerId})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(Activity activity);
    
    /**
     * 更新活动信息
     */
    @Update("UPDATE activities SET title = #{title}, content = #{content}, image = #{image}, " +
            "start_time = #{startTime}, end_time = #{endTime}, location = #{location}, " +
            "participant_limit = #{participantLimit}, current_participants = #{currentParticipants}, " +
            "status = #{status}, update_time = CURRENT_TIMESTAMP WHERE id = #{id}")
    int update(Activity activity);
    
    /**
     * 删除活动
     */
    @Delete("DELETE FROM activities WHERE id = #{id}")
    int deleteById(Long id);
    
    /**
     * 增加参与人数
     */
    @Update("UPDATE activities SET current_participants = current_participants + 1, update_time = CURRENT_TIMESTAMP WHERE id = #{id}")
    int incrementParticipants(Long id);
    
    /**
     * 减少参与人数
     */
    @Update("UPDATE activities SET current_participants = current_participants - 1, update_time = CURRENT_TIMESTAMP WHERE id = #{id}")
    int decrementParticipants(Long id);
    
    /**
     * 获取活动统计信息
     */
    @Select("SELECT COUNT(*) as total_activities, " +
            "SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END) as upcoming_activities, " +
            "SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as ongoing_activities, " +
            "SUM(CASE WHEN status = 2 THEN 1 ELSE 0 END) as completed_activities, " +
            "SUM(current_participants) as total_participants " +
            "FROM activities")
    @Results({
        @Result(property = "totalActivities", column = "total_activities"),
        @Result(property = "upcomingActivities", column = "upcoming_activities"),
        @Result(property = "ongoingActivities", column = "ongoing_activities"),
        @Result(property = "completedActivities", column = "completed_activities"),
        @Result(property = "totalParticipants", column = "total_participants")
    })
    ActivityStatistics getStatistics();
    
    /**
     * 活动统计信息内部类
     */
    class ActivityStatistics {
        private Integer totalActivities;
        private Integer upcomingActivities;
        private Integer ongoingActivities;
        private Integer completedActivities;
        private Integer totalParticipants;
        
        // getter和setter
        public Integer getTotalActivities() { return totalActivities; }
        public void setTotalActivities(Integer totalActivities) { this.totalActivities = totalActivities; }
        
        public Integer getUpcomingActivities() { return upcomingActivities; }
        public void setUpcomingActivities(Integer upcomingActivities) { this.upcomingActivities = upcomingActivities; }
        
        public Integer getOngoingActivities() { return ongoingActivities; }
        public void setOngoingActivities(Integer ongoingActivities) { this.ongoingActivities = ongoingActivities; }
        
        public Integer getCompletedActivities() { return completedActivities; }
        public void setCompletedActivities(Integer completedActivities) { this.completedActivities = completedActivities; }
        
        public Integer getTotalParticipants() { return totalParticipants; }
        public void setTotalParticipants(Integer totalParticipants) { this.totalParticipants = totalParticipants; }
    }
}
