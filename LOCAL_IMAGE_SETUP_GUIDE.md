# 📸 本地图片配置指南（更新版）

## 📋 概述

本指南将帮你配置本地图片系统，让活动图片从本地文件夹加载，同时更新活动内容与图片匹配。

## 📁 文件结构

```
项目根目录/
├── src/main/resources/static/img/PublicWelfare/
│   ├── 儿童阅读日.png
│   ├── 爱心助老活动.png
│   ├── 环保宣传活动.png
│   ├── 社区义诊活动.png
│   └── 社区清洁日.png
├── update_activity_images.sql          # 数据库更新脚本（包含内容更新）
└── LOCAL_IMAGE_SETUP_GUIDE.md         # 本指南
```

## 🔧 配置步骤

### 1. 执行数据库更新脚本

运行 `update_activity_images.sql` 脚本来更新活动表中的图片路径和内容：

```sql
-- 在MySQL中执行以下命令
source update_activity_images.sql;

-- 这个脚本会同时更新：
-- ✅ 活动标题 - 更符合图片主题
-- ✅ 活动内容 - 与图片内容匹配
-- ✅ 图片路径 - 指向本地图片文件
```

### 2. 验证图片文件

确保以下图片文件存在于 `src/main/resources/static/img/PublicWelfare/` 目录中：

- ✅ 儿童阅读日.png
- ✅ 爱心助老活动.png  
- ✅ 环保宣传活动.png
- ✅ 社区义诊活动.png
- ✅ 社区清洁日.png

### 3. 重启应用

```bash
mvn spring-boot:run
```

## 🎯 更新后的活动内容

执行脚本后，活动将更新为以下内容：

### 1. 社区义诊活动 - 万行公益基金会健康服务
- **图片**: 社区义诊活动.png
- **内容**: 专业医疗团队提供免费义诊服务，包括健康检查、血压血糖测量等

### 2. 爱心助老活动 - 关爱社区老年人  
- **图片**: 爱心助老活动.png
- **内容**: 为独居老人提供生活帮助和精神关怀，上门服务和陪伴

### 3. 社区清洁日 - 美化家园环境
- **图片**: 社区清洁日.png
- **内容**: 组织居民参与社区清洁，清理垃圾、整理绿化带

### 4. 环保宣传活动 - 绿色生活倡导
- **图片**: 环保宣传活动.png
- **内容**: 环保知识普及，垃圾分类指导，倡导绿色低碳生活

### 5. 儿童阅读日 - 书香伴成长
- **图片**: 儿童阅读日.png
- **内容**: 儿童阅读推广，故事分享、亲子阅读活动

## 🌐 访问路径

配置完成后，图片将通过以下路径访问：

- **数据库存储路径**: `/img/PublicWelfare/社区义诊活动.png`
- **实际访问URL**: `http://localhost:8090/img/PublicWelfare/社区义诊活动.png`
- **物理文件路径**: `src/main/resources/static/img/PublicWelfare/社区义诊活动.png`

## 🔍 验证配置

### 1. 检查图片API

访问以下API来验证图片配置：

```bash
# 获取所有PublicWelfare图片列表
GET http://localhost:8090/api/images/public-welfare

# 验证特定图片是否存在
GET http://localhost:8090/api/images/validate?path=/img/PublicWelfare/社区义诊活动.png
```

### 2. 检查活动数据

访问活动API查看更新后的数据：

```bash
GET http://localhost:8090/api/activities
```

返回的数据中，应该包含更新后的标题、内容和图片路径：
```json
{
  "id": 1,
  "title": "社区义诊活动 - 万行公益基金会健康服务",
  "description": "万行公益基金会联合专业医疗团队，为社区居民提供免费义诊服务...",
  "image": "/img/PublicWelfare/社区义诊活动.png",
  ...
}
```

### 3. 前端显示测试

访问活动中心页面：
```
http://localhost:8090/acivies.html
```

检查活动卡片是否正确显示：
- ✅ 更新后的活动标题
- ✅ 匹配的活动内容
- ✅ 本地图片正确加载

## 🎨 内容与图片匹配说明

| 活动ID | 图片文件 | 活动主题 | 内容重点 |
|--------|----------|----------|----------|
| 1,6 | 社区义诊活动.png | 医疗健康 | 免费义诊、健康检查、专科服务 |
| 2,7 | 爱心助老活动.png | 关爱老人 | 生活帮助、精神关怀、健康指导 |
| 3,8 | 社区清洁日.png | 环境整治 | 清洁卫生、美化环境、集体劳动 |
| 4 | 环保宣传活动.png | 环保教育 | 环保知识、绿色生活、垃圾分类 |
| 5 | 儿童阅读日.png | 儿童教育 | 阅读推广、故事分享、亲子活动 |

## 🛠️ 技术实现

### 1. 数据库更新
脚本同时更新三个字段：
- `title` - 活动标题
- `content` - 活动详细内容  
- `image` - 图片路径

### 2. 内容一致性
确保活动的标题、内容、图片三者保持主题一致，提供更好的用户体验。

## 🎉 完成效果

配置完成后，你将获得：

- ✅ **主题一致的活动内容** - 标题、内容、图片完美匹配
- ✅ **本地图片加载** - 更快的加载速度和稳定性
- ✅ **万行基金会品牌化** - 所有活动都体现万行公益的特色
- ✅ **丰富的活动类型** - 涵盖医疗、助老、环保、教育等多个领域

现在你只需要：
1. **执行数据库更新脚本** `update_activity_images.sql`
2. **重启应用**
3. **访问活动中心查看全新的活动展示效果**

所有活动内容都已经与对应的图片完美匹配，提供更加专业和一致的用户体验！🎉
