-- ========================================
-- 社区公益服务平台 - 完整数据库初始化脚本
-- 万行公益基金会版本
-- ========================================

-- 创建数据库
CREATE DATABASE IF NOT EXISTS gy CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE gy;

-- ========================================
-- 1. 角色表
-- ========================================
CREATE TABLE roles (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(50) NOT NULL COMMENT '角色名称',
    description VARCHAR(255) COMMENT '角色描述',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) COMMENT '角色表';

-- ========================================
-- 2. 用户表
-- ========================================
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    password VARCHAR(255) NOT NULL COMMENT '密码',
    email VARCHAR(100) UNIQUE COMMENT '邮箱',
    phone VARCHAR(20) COMMENT '电话',
    avatar VARCHAR(255) DEFAULT 'default_avatar.png' COMMENT '头像',
    address VARCHAR(255) COMMENT '地址',
    points INT DEFAULT 0 COMMENT '积分',
    role_id INT DEFAULT 2 COMMENT '角色ID',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (role_id) REFERENCES roles(id),
    INDEX idx_users_email (email),
    INDEX idx_users_phone (phone),
    INDEX idx_users_points (points),
    INDEX idx_users_role_id (role_id)
) COMMENT '用户表';

-- ========================================
-- 3. 组织表
-- ========================================
CREATE TABLE organizations (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL COMMENT '组织名称',
    description TEXT COMMENT '组织描述',
    contact_person VARCHAR(50) COMMENT '联系人',
    contact_phone VARCHAR(20) COMMENT '联系电话',
    logo VARCHAR(255) DEFAULT 'default_logo.png' COMMENT '组织logo',
    user_id INT COMMENT '创建者用户ID',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    INDEX idx_organizations_user_id (user_id)
) COMMENT '组织表';

-- ========================================
-- 4. 活动表（优化版本）
-- ========================================
CREATE TABLE activities (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(200) NOT NULL COMMENT '活动标题',
    content TEXT COMMENT '活动内容',
    image VARCHAR(500) COMMENT '活动图片',
    start_time DATETIME NOT NULL COMMENT '开始时间',
    end_time DATETIME NOT NULL COMMENT '结束时间',
    location VARCHAR(200) COMMENT '活动地点',
    participant_limit INT DEFAULT 50 COMMENT '参与人数限制',
    current_participants INT DEFAULT 0 COMMENT '当前参与人数',
    status TINYINT DEFAULT 0 COMMENT '状态：0-未开始，1-进行中，2-已结束',
    organizer_id INT COMMENT '组织者ID',
    category VARCHAR(50) DEFAULT 'training' COMMENT '活动类型：training-培训, donation-捐赠, volunteer-志愿服务, lecture-讲座',
    registration_deadline DATETIME COMMENT '报名截止时间',
    fee DECIMAL(10,2) DEFAULT 0.00 COMMENT '活动费用',
    tags VARCHAR(500) COMMENT '活动标签，用逗号分隔',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (organizer_id) REFERENCES organizations(id),
    
    -- 性能优化索引
    INDEX idx_activities_status (status),
    INDEX idx_activities_start_time (start_time),
    INDEX idx_activities_create_time (create_time),
    INDEX idx_activities_category (category),
    INDEX idx_activities_status_time (status, start_time),
    INDEX idx_activities_organizer_id (organizer_id),
    
    -- 全文搜索索引
    FULLTEXT idx_activities_search (title, content),
    
    -- 业务约束
    CONSTRAINT chk_participants CHECK (current_participants <= participant_limit),
    CONSTRAINT chk_time_order CHECK (end_time > start_time),
    CONSTRAINT chk_status_valid CHECK (status IN (0, 1, 2))
) COMMENT '活动表';

-- ========================================
-- 5. 活动报名表
-- ========================================
CREATE TABLE activity_registrations (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL COMMENT '用户ID',
    activity_id BIGINT NOT NULL COMMENT '活动ID',
    status TINYINT DEFAULT 1 COMMENT '报名状态：1-已报名，2-已取消',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (activity_id) REFERENCES activities(id),
    UNIQUE KEY uk_user_activity (user_id, activity_id),
    
    -- 性能优化索引
    INDEX idx_registrations_user_id (user_id),
    INDEX idx_registrations_activity_id (activity_id),
    INDEX idx_registrations_status (status),
    INDEX idx_registrations_create_time (create_time)
) COMMENT '活动报名表';

-- ========================================
-- 6. 志愿服务表
-- ========================================
CREATE TABLE volunteer_services (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL COMMENT '志愿者ID',
    activity_id BIGINT NOT NULL COMMENT '活动ID',
    service_hours DECIMAL(5,2) DEFAULT 0 COMMENT '服务时长',
    description TEXT COMMENT '服务描述',
    status TINYINT DEFAULT 1 COMMENT '状态：1-进行中，2-已完成',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (activity_id) REFERENCES activities(id),
    
    -- 性能优化索引
    INDEX idx_volunteer_user_id (user_id),
    INDEX idx_volunteer_activity_id (activity_id),
    INDEX idx_volunteer_create_time (create_time)
) COMMENT '志愿服务表';

-- ========================================
-- 7. 插入基础数据
-- ========================================

-- 插入角色数据
INSERT INTO roles (id, name, description) VALUES 
(1, '管理员', '系统管理员，拥有所有权限'),
(2, '普通用户', '普通用户，可以参与活动'),
(3, '组织者', '活动组织者，可以创建和管理活动');

-- 插入用户数据
INSERT INTO users (id, username, password, email, phone, avatar, address, points, role_id) VALUES 
(1, 'admin', 'admin123', '<EMAIL>', '13800138000', 'default_avatar.png', '深圳市南山区', 100, 1),
(2, 'wanxing_org', 'wanxing123', '<EMAIL>', '0755-12345678', 'wanxing_logo.png', '深圳市南山区科技园', 500, 3),
(3, 'volunteer1', 'vol123', '<EMAIL>', '13800138001', 'default_avatar.png', '深圳市福田区', 80, 2),
(4, 'volunteer2', 'vol123', '<EMAIL>', '13800138002', 'default_avatar.png', '深圳市罗湖区', 60, 2),
(5, 'volunteer3', 'vol123', '<EMAIL>', '13800138003', 'default_avatar.png', '深圳市宝安区', 40, 2);

-- 插入组织数据
INSERT INTO organizations (id, name, description, contact_person, contact_phone, logo, user_id) VALUES 
(1, '万行公益基金会', '万行公益基金会致力于社区公益服务，专注于急救培训和AED设备捐赠，提升社区应急救护能力。我们的使命是让更多人掌握急救技能，在关键时刻能够挽救生命。', '张主任', '0755-12345678', 'wanxing_logo.png', 2);

-- ========================================
-- 8. 插入活动数据（万行基金会风格）
-- ========================================
INSERT INTO activities (title, content, image, start_time, end_time, location, participant_limit, current_participants, status, organizer_id, category, registration_deadline, fee, tags) VALUES 

('AED急救培训走进深圳湾创业广场', 
'应深圳湾创业广场党委邀请，万行公益基金会举办急救知识培训活动，重点培训自动体外除颤器（AED）的使用能力。本次培训将邀请专业的急救医师和培训师，通过理论讲解和实操演练相结合的方式，让参与者掌握基本的心肺复苏技能和AED设备的正确使用方法。培训结束后，参与者将获得急救技能认证证书。', 
'https://picsum.photos/600/400?random=1', 
'2025-06-05 14:00:00', '2025-06-05 17:00:00', '深圳湾创业广场', 50, 25, 0, 1, 'training', '2025-06-04 23:59:59', 0.00, 'AED,急救,培训,心肺复苏'),

('万众益心，救在身边 - 社区AED设备捐赠', 
'为社区配置AED设备并开展急救知识培训，让更多人懂急救、会急救，提升社区应急救护能力。本次活动将为万科城小区捐赠2台AED设备，并为小区居民提供免费的急救知识培训。活动包括设备安装、使用培训、日常维护指导等环节。', 
'https://picsum.photos/600/400?random=2', 
'2025-05-30 09:00:00', '2025-05-30 12:00:00', '万科城小区', 30, 18, 1, 1, 'donation', '2025-05-29 23:59:59', 0.00, 'AED,捐赠,社区,设备'),

('应急医疗救援培训导师师资班', 
'培养专业的急救培训导师，提升志愿者的急救教学能力，为社区急救培训提供师资保障。本次师资班将邀请国内知名急救专家授课，内容包括急救理论、教学方法、实操技能、心理疏导等多个方面。完成培训的学员将获得急救培训导师资格认证。', 
'https://picsum.photos/600/400?random=3', 
'2025-05-28 09:00:00', '2025-05-30 17:00:00', '宝安区应急医疗救援培训中心', 20, 20, 2, 1, 'training', '2025-05-27 23:59:59', 0.00, '师资,培训,急救,导师'),

('社区急救知识普及讲座', 
'面向社区居民开展急救知识普及讲座，提高公众对急救重要性的认识，推广急救技能。讲座将通过生动的案例分析、互动问答、现场演示等方式，让居民了解常见急救场景的处理方法，掌握基本的急救技能。', 
'https://picsum.photos/600/400?random=4', 
'2025-06-15 19:00:00', '2025-06-15 21:00:00', '南山区文化中心', 100, 8, 0, 1, 'lecture', '2025-06-14 23:59:59', 0.00, '讲座,急救,知识,普及'),

('校园AED设备维护志愿服务', 
'组织志愿者对校园内的AED设备进行定期检查和维护，确保设备正常运行，保障校园安全。志愿者将接受专业的设备维护培训，学习AED设备的日常检查、清洁保养、故障排除等技能。', 
'https://picsum.photos/600/400?random=5', 
'2025-05-20 14:00:00', '2025-05-20 16:00:00', '深圳大学', 15, 15, 2, 1, 'volunteer', '2025-05-19 23:59:59', 0.00, '志愿服务,AED,维护,校园'),

('企业员工急救技能培训', 
'为企业员工提供专业的急救技能培训，包括心肺复苏、AED使用等，提升企业应急救护水平。培训将结合企业实际工作环境，设计针对性的急救场景演练，帮助员工掌握实用的急救技能。', 
'https://picsum.photos/600/400?random=6', 
'2025-06-20 14:00:00', '2025-06-20 17:00:00', '腾讯滨海大厦', 40, 12, 0, 1, 'training', '2025-06-19 23:59:59', 0.00, '企业培训,急救,员工,技能'),

('社区应急救护志愿者招募', 
'招募热心公益的社区居民成为应急救护志愿者，经过专业培训后参与社区急救知识普及和应急救援工作。志愿者将接受系统的急救技能培训，并定期参与社区急救知识宣传活动。', 
'https://picsum.photos/600/400?random=7', 
'2025-06-10 10:00:00', '2025-06-10 12:00:00', '南山区社区服务中心', 25, 5, 0, 1, 'volunteer', '2025-06-09 23:59:59', 0.00, '志愿者,招募,社区,救护'),

('AED设备使用技能竞赛', 
'举办AED设备使用技能竞赛，通过竞赛形式提高公众对急救技能的关注度和参与度。竞赛将设置个人赛和团体赛，优胜者将获得奖品和荣誉证书。', 
'https://picsum.photos/600/400?random=8', 
'2025-07-01 14:00:00', '2025-07-01 17:00:00', '深圳市民中心', 60, 0, 0, 1, 'training', '2025-06-30 23:59:59', 0.00, '竞赛,AED,技能,比赛');

-- ========================================
-- 9. 插入报名数据
-- ========================================
INSERT INTO activity_registrations (user_id, activity_id, status) VALUES 
(3, 1, 1), (4, 1, 1), (5, 1, 1),
(3, 2, 1), (4, 2, 1),
(3, 3, 1), (4, 3, 1), (5, 3, 1),
(3, 4, 1), (4, 4, 1),
(3, 5, 1), (4, 5, 1), (5, 5, 1),
(3, 6, 1), (4, 6, 1),
(3, 7, 1);

-- ========================================
-- 10. 创建性能优化视图
-- ========================================

-- 活动统计视图
CREATE VIEW v_activity_statistics AS
SELECT 
    COUNT(*) as total_activities,
    SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END) as upcoming_activities,
    SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as ongoing_activities,
    SUM(CASE WHEN status = 2 THEN 1 ELSE 0 END) as completed_activities,
    SUM(current_participants) as total_participants,
    AVG(current_participants) as avg_participants,
    COUNT(DISTINCT organizer_id) as total_organizers
FROM activities;

-- 用户参与统计视图
CREATE VIEW v_user_participation AS
SELECT 
    u.id,
    u.username,
    u.points,
    COUNT(ar.id) as participated_activities,
    SUM(CASE WHEN ar.status = 1 THEN 1 ELSE 0 END) as active_registrations,
    MAX(ar.create_time) as last_registration_time
FROM users u
LEFT JOIN activity_registrations ar ON u.id = ar.user_id
GROUP BY u.id, u.username, u.points;

-- 热门活动视图
CREATE VIEW v_popular_activities AS
SELECT 
    a.*,
    (a.current_participants / a.participant_limit * 100) as participation_rate,
    COUNT(ar.id) as registration_count
FROM activities a
LEFT JOIN activity_registrations ar ON a.id = ar.activity_id
GROUP BY a.id
ORDER BY participation_rate DESC, registration_count DESC;

-- ========================================
-- 11. 创建存储过程
-- ========================================

DELIMITER //

-- 用户报名活动存储过程
CREATE PROCEDURE sp_join_activity(
    IN p_user_id INT,
    IN p_activity_id BIGINT,
    OUT p_result INT,
    OUT p_message VARCHAR(255)
)
BEGIN
    DECLARE v_current_participants INT;
    DECLARE v_participant_limit INT;
    DECLARE v_registration_count INT;
    DECLARE v_activity_status TINYINT;
    
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SET p_result = -1;
        SET p_message = '报名失败，系统错误';
    END;
    
    START TRANSACTION;
    
    -- 检查活动是否存在
    SELECT current_participants, participant_limit, status 
    INTO v_current_participants, v_participant_limit, v_activity_status
    FROM activities WHERE id = p_activity_id;
    
    IF v_current_participants IS NULL THEN
        SET p_result = 0;
        SET p_message = '活动不存在';
        ROLLBACK;
    ELSEIF v_activity_status != 0 THEN
        SET p_result = 0;
        SET p_message = '活动已开始或已结束，无法报名';
        ROLLBACK;
    ELSE
        -- 检查是否已报名
        SELECT COUNT(*) INTO v_registration_count
        FROM activity_registrations 
        WHERE user_id = p_user_id AND activity_id = p_activity_id;
        
        IF v_registration_count > 0 THEN
            SET p_result = 0;
            SET p_message = '您已报名该活动';
            ROLLBACK;
        ELSEIF v_current_participants >= v_participant_limit THEN
            SET p_result = 0;
            SET p_message = '活动名额已满';
            ROLLBACK;
        ELSE
            -- 插入报名记录
            INSERT INTO activity_registrations (user_id, activity_id, status)
            VALUES (p_user_id, p_activity_id, 1);
            
            -- 更新活动参与人数
            UPDATE activities 
            SET current_participants = current_participants + 1
            WHERE id = p_activity_id;
            
            -- 增加用户积分
            UPDATE users 
            SET points = points + 10
            WHERE id = p_user_id;
            
            SET p_result = 1;
            SET p_message = '报名成功';
            COMMIT;
        END IF;
    END IF;
END //

DELIMITER ;

-- ========================================
-- 12. 创建触发器
-- ========================================

DELIMITER //

-- 自动更新活动状态触发器
CREATE TRIGGER tr_update_activity_status
BEFORE UPDATE ON activities
FOR EACH ROW
BEGIN
    -- 根据时间自动更新活动状态
    IF NEW.start_time <= NOW() AND NEW.end_time > NOW() THEN
        SET NEW.status = 1; -- 进行中
    ELSEIF NEW.end_time <= NOW() THEN
        SET NEW.status = 2; -- 已结束
    END IF;
END //

DELIMITER ;

-- ========================================
-- 13. 数据库优化设置
-- ========================================

-- 分析表以优化查询计划
ANALYZE TABLE activities;
ANALYZE TABLE activity_registrations;
ANALYZE TABLE users;
ANALYZE TABLE organizations;

-- ========================================
-- 完成提示
-- ========================================
SELECT '数据库初始化完成！' as message,
       (SELECT COUNT(*) FROM activities) as activities_count,
       (SELECT COUNT(*) FROM users) as users_count,
       (SELECT COUNT(*) FROM activity_registrations) as registrations_count;
